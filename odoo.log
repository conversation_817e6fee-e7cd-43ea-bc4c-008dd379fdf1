2025-07-28 21:56:08,101 170868 WARNING ? odoo.http: Logged into database 'moassLive25_2', but dbfilter rejects it; logging session out. 
2025-07-28 21:56:08,103 170868 ERROR odoo18 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\http.py", line 2364, in __call__
    response = request._serve_db()
  File "C:\odoo18\server\odoo\http.py", line 1873, in _serve_db
    rule, args = self.registry['ir.http']._match(self.httprequest.path)
                 ~~~~~~~~~~~~~^^^^^^^^^^^
  File "C:\odoo18\server\odoo\modules\registry.py", line 240, in __getitem__
    return self.models[model_name]
           ~~~~~~~~~~~^^^^^^^^^^^^
KeyError: 'ir.http'
2025-07-28 21:56:08,103 170868 INFO odoo18 werkzeug: 127.0.0.1 - - [28/Jul/2025 21:56:08] "GET /odoo/action-457/1153 HTTP/1.1" 500 - 1 0.001 0.002
2025-07-28 21:56:08,163 170868 WARNING ? odoo.http: Logged into database 'moassLive25_2', but dbfilter rejects it; logging session out. 
2025-07-28 21:56:08,170 170868 ERROR odoo18 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\http.py", line 2364, in __call__
    response = request._serve_db()
  File "C:\odoo18\server\odoo\http.py", line 1873, in _serve_db
    rule, args = self.registry['ir.http']._match(self.httprequest.path)
                 ~~~~~~~~~~~~~^^^^^^^^^^^
  File "C:\odoo18\server\odoo\modules\registry.py", line 240, in __getitem__
    return self.models[model_name]
           ~~~~~~~~~~~^^^^^^^^^^^^
KeyError: 'ir.http'
2025-07-28 21:56:08,172 170868 INFO odoo18 werkzeug: 127.0.0.1 - - [28/Jul/2025 21:56:08] "GET /favicon.ico HTTP/1.1" 500 - 1 0.002 0.009
2025-07-28 21:56:09,706 170868 WARNING ? odoo.http: Logged into database 'moassLive25_2', but dbfilter rejects it; logging session out. 
2025-07-28 21:56:09,709 170868 ERROR odoo18 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\http.py", line 2364, in __call__
    response = request._serve_db()
  File "C:\odoo18\server\odoo\http.py", line 1873, in _serve_db
    rule, args = self.registry['ir.http']._match(self.httprequest.path)
                 ~~~~~~~~~~~~~^^^^^^^^^^^
  File "C:\odoo18\server\odoo\modules\registry.py", line 240, in __getitem__
    return self.models[model_name]
           ~~~~~~~~~~~^^^^^^^^^^^^
KeyError: 'ir.http'
2025-07-28 21:56:09,710 170868 INFO odoo18 werkzeug: 127.0.0.1 - - [28/Jul/2025 21:56:09] "GET /web/service-worker.js HTTP/1.1" 500 - 1 0.001 0.004
2025-07-28 21:56:40,026 163692 INFO ? odoo: Odoo version 18.0-20241204 
2025-07-28 21:56:40,026 163692 INFO ? odoo: Using configuration file at C:\odoo18\server\odoo.conf 
2025-07-28 21:56:40,026 163692 INFO ? odoo: addons paths: ['C:\\odoo18\\server\\odoo\\addons', 'c:\\odoo18\\sessions\\addons\\18.0', 'c:\\odoo18\\server\\odoo\\addons', 'c:\\odoo18\\server\\enterprise18', 'c:\\odoo18\\server\\custom_moass_live_5'] 
2025-07-28 21:56:40,026 163692 INFO ? odoo: database: openpg@localhost:5432 
2025-07-28 21:56:40,026 163692 WARNING ? odoo: Python 3.13 is not officially supported, please use Python 3.12 instead 
2025-07-28 21:56:40,192 163692 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo18\thirdparty\wkhtmltopdf.exe 
2025-07-28 21:56:40,216 163692 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-07-28 21:56:40,356 163692 WARNING ? odoo.addons.base.models.res_currency: The num2words python library is not installed, amount-to-text features won't be fully available. 
2025-07-28 21:56:40,381 163692 WARNING ? odoo.addons.web.models.res_partner: `vobject` Python module not found, vcard file generation disabled. Consider installing this module if you want to generate vcard files 
2025-07-28 21:56:41,750 163692 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8069 
2025-07-28 21:56:41,865 163692 ERROR odoo18 odoo.modules.loading: Database odoo18 not initialized, you can force it with `-i base` 
2025-07-28 21:56:41,865 163692 INFO odoo18 odoo.modules.registry: Registry loaded in 0.113s 
2025-07-28 21:57:01,729 159908 INFO ? odoo: Odoo version 18.0-20241204 
2025-07-28 21:57:01,729 159908 INFO ? odoo: Using configuration file at C:\odoo18\server\odoo.conf 
2025-07-28 21:57:01,729 159908 INFO ? odoo: addons paths: ['C:\\odoo18\\server\\odoo\\addons', 'c:\\odoo18\\sessions\\addons\\18.0', 'c:\\odoo18\\server\\odoo\\addons', 'c:\\odoo18\\server\\enterprise18', 'c:\\odoo18\\server\\custom_moass_live_5'] 
2025-07-28 21:57:01,729 159908 INFO ? odoo: database: openpg@localhost:5432 
2025-07-28 21:57:01,729 159908 WARNING ? odoo: Python 3.13 is not officially supported, please use Python 3.12 instead 
2025-07-28 21:57:01,829 159908 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo18\thirdparty\wkhtmltopdf.exe 
2025-07-28 21:57:01,855 159908 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-07-28 21:57:01,976 159908 WARNING ? odoo.addons.base.models.res_currency: The num2words python library is not installed, amount-to-text features won't be fully available. 
2025-07-28 21:57:02,020 159908 WARNING ? odoo.addons.web.models.res_partner: `vobject` Python module not found, vcard file generation disabled. Consider installing this module if you want to generate vcard files 
2025-07-28 21:57:03,410 159908 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8069 
2025-07-28 21:58:21,000 198852 INFO ? odoo: Odoo version 18.0-20241204 
2025-07-28 21:58:21,000 198852 INFO ? odoo: Using configuration file at C:\odoo18\server\odoo.conf 
2025-07-28 21:58:21,000 198852 INFO ? odoo: addons paths: ['C:\\odoo18\\server\\odoo\\addons', 'c:\\odoo18\\sessions\\addons\\18.0', 'c:\\odoo18\\server\\enterprise183', 'c:\\odoo18\\server\\custom_moass_live_5', 'c:\\odoo18\\server\\odoo\\addons'] 
2025-07-28 21:58:21,000 198852 INFO ? odoo: database: openpg@localhost:5432 
2025-07-28 21:58:21,000 198852 WARNING ? odoo: Python 3.13 is not officially supported, please use Python 3.12 instead 
2025-07-28 21:58:21,126 198852 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo18\thirdparty\wkhtmltopdf.exe 
2025-07-28 21:58:21,153 198852 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-07-28 21:58:21,324 198852 WARNING ? odoo.addons.base.models.res_currency: The num2words python library is not installed, amount-to-text features won't be fully available. 
2025-07-28 21:58:21,351 198852 WARNING ? odoo.addons.web.models.res_partner: `vobject` Python module not found, vcard file generation disabled. Consider installing this module if you want to generate vcard files 
2025-07-28 21:58:22,808 198852 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8089 
2025-07-28 21:58:35,773 190932 INFO ? odoo: Odoo version 18.0-20241204 
2025-07-28 21:58:35,773 190932 INFO ? odoo: Using configuration file at C:\odoo18\server\odoo.conf 
2025-07-28 21:58:35,774 190932 INFO ? odoo: addons paths: ['C:\\odoo18\\server\\odoo\\addons', 'c:\\odoo18\\sessions\\addons\\18.0', 'c:\\odoo18\\server\\enterprise183', 'c:\\odoo18\\server\\custom_moass_live_5', 'c:\\odoo18\\server\\odoo\\addons'] 
2025-07-28 21:58:35,774 190932 INFO ? odoo: database: openpg@localhost:5432 
2025-07-28 21:58:35,997 190932 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo18\thirdparty\wkhtmltopdf.exe 
2025-07-28 21:58:36,028 190932 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-07-28 21:58:37,745 190932 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8090 
2025-07-28 21:58:37,871 190932 INFO moassLive25_2 odoo.modules.loading: loading 1 modules... 
2025-07-28 21:58:37,909 190932 INFO moassLive25_2 odoo.modules.loading: 1 modules loaded in 0.04s, 0 queries (+0 extra) 
2025-07-28 21:58:37,926 190932 INFO moassLive25_2 odoo.modules.loading: updating modules list 
2025-07-28 21:58:37,940 190932 INFO moassLive25_2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-28 21:58:38,245 190932 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'app_api', defaulting to LGPL-3 
2025-07-28 21:58:38,460 190932 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'branch_cost', defaulting to LGPL-3 
2025-07-28 21:58:38,550 190932 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'cubes_move_restrictions', defaulting to LGPL-3 
2025-07-28 21:58:38,683 190932 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-07-28 21:58:40,806 190932 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'payment_reports_custom', defaulting to LGPL-3 
2025-07-28 21:58:41,142 190932 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'reconciliation_report', defaulting to LGPL-3 
2025-07-28 21:58:42,120 190932 INFO moassLive25_2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['Price Checker Kiosk'] to user __system__ #1 via n/a 
2025-07-28 21:58:42,120 190932 INFO moassLive25_2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['Price Checker Kiosk'] to user __system__ #1 via n/a 
2025-07-28 21:58:45,951 190932 INFO moassLive25_2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-28 21:58:46,016 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.users', <function Users.context_get at 0x000001E1B02A9940>, 1)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x000001E1B01CF560>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-07-28 21:58:46,048 190932 INFO moassLive25_2 odoo.modules.loading: loading 251 modules... 
2025-07-28 21:58:46,048 190932 WARNING moassLive25_2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-28 21:58:46,588 190932 INFO moassLive25_2 odoo.modules.loading: Loading module price_checker_kiosk (55/251) 
2025-07-28 21:58:46,716 190932 INFO moassLive25_2 odoo.modules.loading: loading price_checker_kiosk/security/ir.model.access.csv 
2025-07-28 21:58:46,716 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x000001E1B01CF560>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-07-28 21:58:46,975 190932 INFO moassLive25_2 odoo.modules.loading: loading price_checker_kiosk/views/price_checker_templates.xml 
2025-07-28 21:58:46,986 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x000001E1B01CF560>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-07-28 21:58:47,001 190932 INFO moassLive25_2 odoo.addons.base.models.ir_module: module price_checker_kiosk: no translation for language ar_001 
2025-07-28 21:58:47,009 190932 INFO moassLive25_2 odoo.modules.loading: Module price_checker_kiosk loaded in 0.42s, 49 queries (+49 other) 
2025-07-28 21:58:48,229 190932 WARNING moassLive25_2 py.warnings: C:\odoo18\server\odoo\api.py:466: DeprecationWarning: The model odoo.addons.branch_management.models.purchase is not overriding the create method in batch
  File "C:\odoo18\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\odoo18\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\odoo18\server\odoo\cli\server.py", line 180, in run
    main(args)
  File "C:\odoo18\server\odoo\cli\server.py", line 173, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\odoo18\server\odoo\service\server.py", line 1402, in start
    rc = server.run(preload, stop)
  File "C:\odoo18\server\odoo\service\server.py", line 577, in run
    rc = preload_registries(preload)
  File "C:\odoo18\server\odoo\service\server.py", line 1306, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18\server\odoo\modules\loading.py", line 476, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\odoo18\server\odoo\modules\loading.py", line 364, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\odoo18\server\odoo\modules\loading.py", line 185, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo18\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "c:\odoo18\server\custom_moass_live_5\branch_management\__init__.py", line 1, in <module>
    from . import models
  File "c:\odoo18\server\custom_moass_live_5\branch_management\models\__init__.py", line 6, in <module>
    from . import purchase
  File "c:\odoo18\server\custom_moass_live_5\branch_management\models\purchase.py", line 3, in <module>
    class PurchaseOrder(models.Model):
  File "c:\odoo18\server\custom_moass_live_5\branch_management\models\purchase.py", line 72, in PurchaseOrder
    @api.model
  File "C:\odoo18\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\odoo18\server\odoo\api.py", line 466, in model_create_single
    warnings.warn(
 
2025-07-28 21:58:48,380 190932 WARNING moassLive25_2 odoo.models: The model notification.center has no _description 
2025-07-28 21:58:48,424 190932 WARNING moassLive25_2 odoo.models: The model location.stock.wizard has no _description 
2025-07-28 21:58:48,471 190932 INFO moassLive25_2 odoo.modules.loading: 251 modules loaded in 2.42s, 49 queries (+49 extra) 
2025-07-28 21:58:48,471 190932 INFO moassLive25_2 odoo.modules.loading: loading 251 modules... 
2025-07-28 21:58:48,471 190932 INFO moassLive25_2 odoo.modules.loading: 251 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-28 21:58:48,477 190932 INFO moassLive25_2 odoo.modules.loading: loading 251 modules... 
2025-07-28 21:58:48,477 190932 INFO moassLive25_2 odoo.modules.loading: 251 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-28 21:58:48,644 190932 WARNING moassLive25_2 odoo.fields: Field vip.transfer.partner_id: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 21:58:48,644 190932 WARNING moassLive25_2 odoo.fields: Field vip.transfer.source_warehouse_id: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 21:58:48,644 190932 WARNING moassLive25_2 odoo.fields: Field vip.transfer.state: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 21:58:48,644 190932 WARNING moassLive25_2 odoo.fields: Field notification.center.notification_type: unknown parameter 'states', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 21:58:48,644 190932 WARNING moassLive25_2 odoo.fields: Field notification.center.user_ids: unknown parameter 'states', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 21:58:48,644 190932 WARNING moassLive25_2 odoo.fields: Field branch.cost.report.branch_id with unknown comodel_name 'res.branch' 
2025-07-28 21:58:48,644 190932 WARNING moassLive25_2 odoo.fields: Field branch.transfer.state: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 21:58:48,980 190932 WARNING moassLive25_2 odoo.fields: Field vip.transfer.partner_id: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 21:58:48,980 190932 WARNING moassLive25_2 odoo.fields: Field vip.transfer.source_warehouse_id: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 21:58:48,980 190932 WARNING moassLive25_2 odoo.fields: Field vip.transfer.state: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 21:58:48,980 190932 WARNING moassLive25_2 odoo.fields: Field notification.center.notification_type: unknown parameter 'states', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 21:58:48,980 190932 WARNING moassLive25_2 odoo.fields: Field notification.center.user_ids: unknown parameter 'states', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 21:58:48,980 190932 WARNING moassLive25_2 odoo.fields: Field branch.transfer.state: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 21:58:49,028 190932 INFO moassLive25_2 odoo.modules.loading: Model account.followup.report.handler is declared but cannot be loaded! (Perhaps a module was partially removed or renamed) 
2025-07-28 21:58:49,121 190932 INFO moassLive25_2 odoo.modules.loading: Model account.customer.statement.report.handler is declared but cannot be loaded! (Perhaps a module was partially removed or renamed) 
2025-07-28 21:58:49,817 190932 WARNING moassLive25_2 py.warnings: C:\odoo18\server\odoo\fields.py:834: UserWarning: Field 'partner.ledger.line.env_ref_lines' in dependency of partner.ledger.line.ending_balance should be searchable. This is necessary to determine which records to recompute when partner.ledger.line.balance is modified. You should either make the field searchable, or simplify the field dependency.
  File "C:\odoo18\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\odoo18\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\odoo18\server\odoo\cli\server.py", line 180, in run
    main(args)
  File "C:\odoo18\server\odoo\cli\server.py", line 173, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\odoo18\server\odoo\service\server.py", line 1402, in start
    rc = server.run(preload, stop)
  File "C:\odoo18\server\odoo\service\server.py", line 577, in run
    rc = preload_registries(preload)
  File "C:\odoo18\server\odoo\service\server.py", line 1306, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18\server\odoo\modules\loading.py", line 583, in load_modules
    env['res.groups']._update_user_groups_view()
  File "C:\odoo18\server\odoo\addons\base\models\res_users.py", line 1794, in _update_user_groups_view
    view.with_context(new_context).write({'arch': xml_content})
  File "c:\odoo18\server\enterprise183\web_studio\models\studio_mixin.py", line 33, in write
    res = super(StudioMixin, self).write(vals)
  File "C:\odoo18\server\odoo\addons\base\models\ir_ui_view.py", line 537, in write
    res = super(View, self).write(self._compute_defaults(vals))
  File "C:\odoo18\server\odoo\models.py", line 4677, in write
    if self.pool.is_modifying_relations(field):
  File "C:\odoo18\server\odoo\modules\registry.py", line 533, in is_modifying_relations
    result = field in self._field_triggers and (
  File "C:\odoo18\server\odoo\tools\func.py", line 42, in __get__
    value = self.fget(obj)
  File "C:\odoo18\server\odoo\modules\registry.py", line 514, in _field_triggers
    dependencies = list(field.resolve_depends(self))
  File "C:\odoo18\server\odoo\fields.py", line 834, in resolve_depends
    warnings.warn(
 
2025-07-28 21:58:52,600 190932 INFO moassLive25_2 odoo.modules.loading: Modules loaded. 
2025-07-28 21:58:52,629 190932 INFO moassLive25_2 odoo.modules.registry: Registry changed, signaling through the database 
2025-07-28 21:58:52,629 190932 INFO moassLive25_2 odoo.modules.registry: Registry loaded in 14.881s 
2025-07-28 21:59:30,712 190932 INFO moassLive25_2 odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-07-28 21:59:30,759 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x000001E1B01CF560>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-07-28 21:59:30,780 190932 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 21:59:30] "GET / HTTP/1.1" 303 - 7 0.020 0.055
2025-07-28 21:59:31,069 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\25/25eab71cf1a7250397d5f1b39bbdb69315983ba2 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001E1AF919A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\25/25eab71cf1a7250397d5f1b39bbdb69315983ba2'
2025-07-28 21:59:31,070 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\d9/d91cb0023dd0de9111309aa17eddcd5caff2fa91 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001E1AF919A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\d9/d91cb0023dd0de9111309aa17eddcd5caff2fa91'
2025-07-28 21:59:31,071 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\ca/cad9804f210a8c1e26c8ebab07b8056b9d5ec66b 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001E1AF919A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\ca/cad9804f210a8c1e26c8ebab07b8056b9d5ec66b'
2025-07-28 21:59:31,072 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\70/70633333ff0c9230f06ebf2b3bf7770df6d800d0 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001E1AF919A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\70/70633333ff0c9230f06ebf2b3bf7770df6d800d0'
2025-07-28 21:59:31,072 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\37/3709cc2e0d5df4c199f96c6d07e2aaf29a63bcba 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001E1AF919A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\37/3709cc2e0d5df4c199f96c6d07e2aaf29a63bcba'
2025-07-28 21:59:31,073 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\c2/c21721ec3ec2e8d086eebf804e0ede5f25aa14d7 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001E1AF919A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\c2/c21721ec3ec2e8d086eebf804e0ede5f25aa14d7'
2025-07-28 21:59:31,074 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\f9/f9cf2c8b67d3ebc89ca4d180ef207a248c4e3130 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001E1AF919A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\f9/f9cf2c8b67d3ebc89ca4d180ef207a248c4e3130'
2025-07-28 21:59:31,074 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\7d/7d36617149ed2fe93e9ba984cffe16f468bd7a0e 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001E1AF919A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\7d/7d36617149ed2fe93e9ba984cffe16f468bd7a0e'
2025-07-28 21:59:31,075 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\c2/c2f49a19f3cf050243e62b22cde5072cca84fcaa 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001E1AF919A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\c2/c2f49a19f3cf050243e62b22cde5072cca84fcaa'
2025-07-28 21:59:31,076 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\67/670e9ff9033932ebe066245e9ac5c3382cde45f4 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001E1AF919A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\67/670e9ff9033932ebe066245e9ac5c3382cde45f4'
2025-07-28 21:59:31,076 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\51/517a97a04c19cdd67572ff8a0ef89fad5a2c278e 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001E1AF919A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\51/517a97a04c19cdd67572ff8a0ef89fad5a2c278e'
2025-07-28 21:59:31,077 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\1c/1c96f0109a19f3ff4c0a7486431a79da8c42d8de 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001E1AF919A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\1c/1c96f0109a19f3ff4c0a7486431a79da8c42d8de'
2025-07-28 21:59:31,078 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\50/503be145dcd1c8f6491ae855820d93b8ab8363da 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001E1AF919A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\50/503be145dcd1c8f6491ae855820d93b8ab8363da'
2025-07-28 21:59:31,081 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\1c/1c16002d10f6155661a6ae83417fee29e281fb7b 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001E1AF919A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\1c/1c16002d10f6155661a6ae83417fee29e281fb7b'
2025-07-28 21:59:31,082 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\a4/a4511b76524b49a86d44d27508dd20f056bfe827 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001E1AF919A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\a4/a4511b76524b49a86d44d27508dd20f056bfe827'
2025-07-28 21:59:31,083 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\f3/f31bdf173e5748b875ca5e389e1edba54b4bff25 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001E1AF919A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\f3/f31bdf173e5748b875ca5e389e1edba54b4bff25'
2025-07-28 21:59:31,084 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\74/744fc247b813c44f4cf02357706e88b283cb0f97 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001E1AF919A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\74/744fc247b813c44f4cf02357706e88b283cb0f97'
2025-07-28 21:59:31,085 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\ac/ac2a935c445b4a44ecb11e8b692d41661f9ecae1 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001E1AF919A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\ac/ac2a935c445b4a44ecb11e8b692d41661f9ecae1'
2025-07-28 21:59:31,086 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\3d/3d7a7360fe4a73a90dd3b4ba554a9254ed61e864 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001E1AF919A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\3d/3d7a7360fe4a73a90dd3b4ba554a9254ed61e864'
2025-07-28 21:59:31,087 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\b7/b73a9deb85def8a1a00e4d545c4f79747d78f487 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001E1AF919A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\b7/b73a9deb85def8a1a00e4d545c4f79747d78f487'
2025-07-28 21:59:31,239 190932 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-07-28 21:59:31,239 190932 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'branch_cost', defaulting to LGPL-3 
2025-07-28 21:59:31,286 190932 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'reconciliation_report', defaulting to LGPL-3 
2025-07-28 21:59:31,290 190932 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'app_api', defaulting to LGPL-3 
2025-07-28 21:59:31,297 190932 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'payment_reports_custom', defaulting to LGPL-3 
2025-07-28 21:59:31,304 190932 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'cubes_move_restrictions', defaulting to LGPL-3 
2025-07-28 21:59:32,720 190932 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 21:59:32] "GET /odoo HTTP/1.1" 200 - 127 0.131 1.804
2025-07-28 21:59:39,909 190932 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 21:59:39] "GET /web/webclient/translations/fe4776f82214bf053cf29b0ea44b4179db81878d?lang=en_US HTTP/1.1" 200 - 1 0.008 6.863
2025-07-28 21:59:40,086 190932 INFO moassLive25_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/253849a/web.assets_web.min.css (id:13281) 
2025-07-28 21:59:40,098 190932 INFO moassLive25_2 odoo.addons.base.models.assetsbundle: Deleting attachments [13279] (matching /web/assets/_______/web.assets_web.min.css) because it was replaced with /web/assets/253849a/web.assets_web.min.css 
2025-07-28 21:59:40,264 190932 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 21:59:40] "GET /web/assets/253849a/web.assets_web.min.css HTTP/1.1" 200 - 17 0.168 7.376
2025-07-28 21:59:40,339 190932 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 21:59:40] "GET /web/webclient/load_menus/f1b56be9f0983c41462c67b84c78ffe7b91747846103b6afea712b3cce71b6de HTTP/1.1" 200 - 370 0.276 7.028
2025-07-28 21:59:40,469 190932 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 21:59:40] "GET /price_checker HTTP/1.1" 200 - 38 0.084 0.481
2025-07-28 21:59:45,089 190932 INFO ? werkzeug: 127.0.0.1 - - [28/Jul/2025 21:59:45] "GET /price_checker_kiosk/static/src/css/price_checker.css HTTP/1.1" 200 - 0 0.000 0.022
2025-07-28 21:59:45,152 190932 INFO moassLive25_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/e32c9a6/web.assets_frontend.min.css (id:13282) 
2025-07-28 21:59:45,155 190932 INFO moassLive25_2 odoo.addons.base.models.assetsbundle: Deleting attachments [13280] (matching /web/assets/_______/web.assets_frontend.min.css) because it was replaced with /web/assets/e32c9a6/web.assets_frontend.min.css 
2025-07-28 21:59:45,193 190932 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 21:59:45] "GET /web/assets/e32c9a6/web.assets_frontend.min.css HTTP/1.1" 200 - 10 0.056 4.514
2025-07-28 21:59:52,239 190932 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 21:59:52] "GET /web/assets/aaef59a/web.assets_frontend_minimal.min.js HTTP/1.1" 200 - 3 0.018 0.061
2025-07-28 21:59:52,345 190932 INFO moassLive25_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/45eb00f/web.assets_web_print.min.css (id:13283) 
2025-07-28 21:59:52,348 190932 INFO moassLive25_2 odoo.addons.base.models.assetsbundle: Deleting attachments [13278] (matching /web/assets/_______/web.assets_web_print.min.css) because it was replaced with /web/assets/45eb00f/web.assets_web_print.min.css 
2025-07-28 21:59:52,428 190932 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 21:59:52] "GET /web/assets/45eb00f/web.assets_web_print.min.css HTTP/1.1" 200 - 10 0.195 7.162
2025-07-28 22:00:04,960 190932 INFO moassLive25_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/ff5ee79/web.assets_frontend_lazy.min.js (id:13284) 
2025-07-28 22:00:04,961 190932 INFO moassLive25_2 odoo.addons.base.models.assetsbundle: Deleting attachments [13276] (matching /web/assets/_______/web.assets_frontend_lazy.min.js) because it was replaced with /web/assets/ff5ee79/web.assets_frontend_lazy.min.js 
2025-07-28 22:00:05,234 190932 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:00:05] "GET /web/assets/ff5ee79/web.assets_frontend_lazy.min.js HTTP/1.1" 200 - 10 0.011 12.634
2025-07-28 22:00:05,651 190932 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:00:05] "GET /website/translations/d63638e758a0884d5c31259a8318ac9e44884af9 HTTP/1.1" 200 - 3 0.004 0.013
2025-07-28 22:00:40,897 190932 INFO moassLive25_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/246b68f/web.assets_web.min.js (id:13285) 
2025-07-28 22:00:40,898 190932 INFO moassLive25_2 odoo.addons.base.models.assetsbundle: Deleting attachments [13275] (matching /web/assets/_______/web.assets_web.min.js) because it was replaced with /web/assets/246b68f/web.assets_web.min.js 
2025-07-28 22:00:41,412 190932 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:00:41] "GET /web/assets/246b68f/web.assets_web.min.js HTTP/1.1" 200 - 13 0.119 68.258
2025-07-28 22:00:42,011 190932 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:00:42] "POST /hr_attendance/attendance_user_data HTTP/1.1" 200 - 7 0.014 0.016
2025-07-28 22:00:42,055 190932 INFO ? werkzeug: 127.0.0.1 - - [28/Jul/2025 22:00:42] "GET /web/static/img/default_icon_app.png HTTP/1.1" 200 - 0 0.000 0.003
2025-07-28 22:00:42,055 190932 INFO ? werkzeug: 127.0.0.1 - - [28/Jul/2025 22:00:42] "GET /web_enterprise/static/img/background-light.svg HTTP/1.1" 200 - 0 0.000 0.016
2025-07-28 22:00:42,152 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\10/10b00a9b7504892eb1bca5d9b6c2848842d67ea6 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\10/10b00a9b7504892eb1bca5d9b6c2848842d67ea6'
2025-07-28 22:00:42,153 190932 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\10/10b00a9b7504892eb1bca5d9b6c2848842d67ea6 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\10/10b00a9b7504892eb1bca5d9b6c2848842d67ea6'
2025-07-28 22:00:42,161 190932 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:00:42] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 16 0.065 0.038
2025-07-28 22:00:42,162 190932 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:00:42] "GET /web/image/res.partner/3/avatar_128?unique=1751192354000 HTTP/1.1" 200 - 9 0.076 0.030
2025-07-28 22:00:42,210 190932 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:00:42] "POST /mail/data HTTP/1.1" 200 - 46 0.135 0.053
2025-07-28 22:00:42,351 190932 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:00:42] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.013 0.017
2025-07-28 22:00:42,368 190932 INFO ? werkzeug: 127.0.0.1 - - [28/Jul/2025 22:00:42] "GET /web/static/img/odoo-icon-192x192.png HTTP/1.1" 200 - 0 0.000 0.007
2025-07-28 22:00:42,492 190932 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:00:42] "GET /bus/websocket_worker_bundle?v=18.0-2 HTTP/1.1" 200 - 4 0.004 0.009
2025-07-28 22:00:42,510 190932 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:00:42] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.007
2025-07-28 22:00:42,689 190932 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:00:42] "GET /odoo/offline HTTP/1.1" 200 - 6 0.004 0.014
2025-07-28 22:00:42,832 190932 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:00:42] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 1 0.000 0.009
2025-07-28 22:00:42,907 190932 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-07-28 22:05:43,430 200164 INFO ? odoo: Odoo version 18.0-20241204 
2025-07-28 22:05:43,430 200164 INFO ? odoo: Using configuration file at C:\odoo18\server\odoo.conf 
2025-07-28 22:05:43,430 200164 INFO ? odoo: addons paths: ['C:\\odoo18\\server\\odoo\\addons', 'c:\\odoo18\\sessions\\addons\\18.0', 'c:\\odoo18\\server\\enterprise183', 'c:\\odoo18\\server\\custom_moass_live_5', 'c:\\odoo18\\server\\odoo\\addons'] 
2025-07-28 22:05:43,430 200164 INFO ? odoo: database: openpg@localhost:5432 
2025-07-28 22:05:43,606 200164 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo18\thirdparty\wkhtmltopdf.exe 
2025-07-28 22:05:43,638 200164 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-07-28 22:05:45,187 200164 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8089 
2025-07-28 22:05:45,268 200164 INFO moassLive25_2 odoo.modules.loading: loading 1 modules... 
2025-07-28 22:05:45,303 200164 INFO moassLive25_2 odoo.modules.loading: 1 modules loaded in 0.04s, 0 queries (+0 extra) 
2025-07-28 22:05:45,320 200164 INFO moassLive25_2 odoo.modules.loading: updating modules list 
2025-07-28 22:05:45,325 200164 INFO moassLive25_2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-28 22:05:45,570 200164 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'app_api', defaulting to LGPL-3 
2025-07-28 22:05:45,659 200164 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'branch_cost', defaulting to LGPL-3 
2025-07-28 22:05:45,707 200164 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'cubes_move_restrictions', defaulting to LGPL-3 
2025-07-28 22:05:45,906 200164 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-07-28 22:05:48,704 200164 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'payment_reports_custom', defaulting to LGPL-3 
2025-07-28 22:05:49,156 200164 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'reconciliation_report', defaulting to LGPL-3 
2025-07-28 22:05:50,900 200164 INFO moassLive25_2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['Price Checker Kiosk'] to user __system__ #1 via n/a 
2025-07-28 22:05:50,901 200164 INFO moassLive25_2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['Price Checker Kiosk'] to user __system__ #1 via n/a 
2025-07-28 22:05:55,641 200164 INFO moassLive25_2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-28 22:05:55,699 200164 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.users', <function Users.context_get at 0x0000021357CF9940>, 1)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x0000021357C1F560>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-07-28 22:05:55,745 200164 INFO moassLive25_2 odoo.modules.loading: loading 251 modules... 
2025-07-28 22:05:55,764 200164 WARNING moassLive25_2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-28 22:05:56,644 200164 INFO moassLive25_2 odoo.modules.loading: Loading module price_checker_kiosk (55/251) 
2025-07-28 22:05:56,849 200164 INFO moassLive25_2 odoo.modules.loading: loading price_checker_kiosk/security/ir.model.access.csv 
2025-07-28 22:05:56,853 200164 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x0000021357C1F560>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-07-28 22:05:57,289 200164 INFO moassLive25_2 odoo.modules.loading: loading price_checker_kiosk/views/price_checker_templates.xml 
2025-07-28 22:05:57,333 200164 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x0000021357C1F560>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-07-28 22:05:57,357 200164 INFO moassLive25_2 odoo.addons.base.models.ir_module: module price_checker_kiosk: no translation for language ar_001 
2025-07-28 22:05:57,393 200164 INFO moassLive25_2 odoo.modules.loading: Module price_checker_kiosk loaded in 0.75s, 50 queries (+50 other) 
2025-07-28 22:05:58,935 200164 WARNING moassLive25_2 py.warnings: C:\odoo18\server\odoo\api.py:466: DeprecationWarning: The model odoo.addons.branch_management.models.purchase is not overriding the create method in batch
  File "C:\odoo18\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\odoo18\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\odoo18\server\odoo\cli\server.py", line 180, in run
    main(args)
  File "C:\odoo18\server\odoo\cli\server.py", line 173, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\odoo18\server\odoo\service\server.py", line 1402, in start
    rc = server.run(preload, stop)
  File "C:\odoo18\server\odoo\service\server.py", line 577, in run
    rc = preload_registries(preload)
  File "C:\odoo18\server\odoo\service\server.py", line 1306, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18\server\odoo\modules\loading.py", line 476, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\odoo18\server\odoo\modules\loading.py", line 364, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\odoo18\server\odoo\modules\loading.py", line 185, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo18\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "c:\odoo18\server\custom_moass_live_5\branch_management\__init__.py", line 1, in <module>
    from . import models
  File "c:\odoo18\server\custom_moass_live_5\branch_management\models\__init__.py", line 6, in <module>
    from . import purchase
  File "c:\odoo18\server\custom_moass_live_5\branch_management\models\purchase.py", line 3, in <module>
    class PurchaseOrder(models.Model):
  File "c:\odoo18\server\custom_moass_live_5\branch_management\models\purchase.py", line 72, in PurchaseOrder
    @api.model
  File "C:\odoo18\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\odoo18\server\odoo\api.py", line 466, in model_create_single
    warnings.warn(
 
2025-07-28 22:05:59,147 200164 WARNING moassLive25_2 odoo.models: The model notification.center has no _description 
2025-07-28 22:05:59,201 200164 WARNING moassLive25_2 odoo.models: The model location.stock.wizard has no _description 
2025-07-28 22:05:59,242 200164 INFO moassLive25_2 odoo.modules.loading: 251 modules loaded in 3.50s, 50 queries (+50 extra) 
2025-07-28 22:05:59,247 200164 INFO moassLive25_2 odoo.modules.loading: loading 251 modules... 
2025-07-28 22:05:59,248 200164 INFO moassLive25_2 odoo.modules.loading: 251 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-28 22:05:59,252 200164 INFO moassLive25_2 odoo.modules.loading: loading 251 modules... 
2025-07-28 22:05:59,253 200164 INFO moassLive25_2 odoo.modules.loading: 251 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-28 22:05:59,620 200164 WARNING moassLive25_2 odoo.fields: Field vip.transfer.partner_id: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:05:59,621 200164 WARNING moassLive25_2 odoo.fields: Field vip.transfer.source_warehouse_id: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:05:59,621 200164 WARNING moassLive25_2 odoo.fields: Field vip.transfer.state: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:05:59,621 200164 WARNING moassLive25_2 odoo.fields: Field notification.center.notification_type: unknown parameter 'states', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:05:59,621 200164 WARNING moassLive25_2 odoo.fields: Field notification.center.user_ids: unknown parameter 'states', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:05:59,621 200164 WARNING moassLive25_2 odoo.fields: Field branch.cost.report.branch_id with unknown comodel_name 'res.branch' 
2025-07-28 22:05:59,622 200164 WARNING moassLive25_2 odoo.fields: Field branch.transfer.state: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:06:00,185 200164 WARNING moassLive25_2 odoo.fields: Field vip.transfer.partner_id: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:06:00,185 200164 WARNING moassLive25_2 odoo.fields: Field vip.transfer.source_warehouse_id: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:06:00,185 200164 WARNING moassLive25_2 odoo.fields: Field vip.transfer.state: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:06:00,187 200164 WARNING moassLive25_2 odoo.fields: Field notification.center.notification_type: unknown parameter 'states', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:06:00,187 200164 WARNING moassLive25_2 odoo.fields: Field notification.center.user_ids: unknown parameter 'states', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:06:00,187 200164 WARNING moassLive25_2 odoo.fields: Field branch.transfer.state: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:06:00,303 200164 INFO moassLive25_2 odoo.modules.loading: Model account.followup.report.handler is declared but cannot be loaded! (Perhaps a module was partially removed or renamed) 
2025-07-28 22:06:00,409 200164 INFO moassLive25_2 odoo.modules.loading: Model account.customer.statement.report.handler is declared but cannot be loaded! (Perhaps a module was partially removed or renamed) 
2025-07-28 22:06:01,166 200164 WARNING moassLive25_2 py.warnings: C:\odoo18\server\odoo\fields.py:834: UserWarning: Field 'partner.ledger.line.env_ref_lines' in dependency of partner.ledger.line.ending_balance should be searchable. This is necessary to determine which records to recompute when partner.ledger.line.balance is modified. You should either make the field searchable, or simplify the field dependency.
  File "C:\odoo18\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\odoo18\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\odoo18\server\odoo\cli\server.py", line 180, in run
    main(args)
  File "C:\odoo18\server\odoo\cli\server.py", line 173, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\odoo18\server\odoo\service\server.py", line 1402, in start
    rc = server.run(preload, stop)
  File "C:\odoo18\server\odoo\service\server.py", line 577, in run
    rc = preload_registries(preload)
  File "C:\odoo18\server\odoo\service\server.py", line 1306, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18\server\odoo\modules\loading.py", line 583, in load_modules
    env['res.groups']._update_user_groups_view()
  File "C:\odoo18\server\odoo\addons\base\models\res_users.py", line 1794, in _update_user_groups_view
    view.with_context(new_context).write({'arch': xml_content})
  File "c:\odoo18\server\enterprise183\web_studio\models\studio_mixin.py", line 33, in write
    res = super(StudioMixin, self).write(vals)
  File "C:\odoo18\server\odoo\addons\base\models\ir_ui_view.py", line 537, in write
    res = super(View, self).write(self._compute_defaults(vals))
  File "C:\odoo18\server\odoo\models.py", line 4677, in write
    if self.pool.is_modifying_relations(field):
  File "C:\odoo18\server\odoo\modules\registry.py", line 533, in is_modifying_relations
    result = field in self._field_triggers and (
  File "C:\odoo18\server\odoo\tools\func.py", line 42, in __get__
    value = self.fget(obj)
  File "C:\odoo18\server\odoo\modules\registry.py", line 514, in _field_triggers
    dependencies = list(field.resolve_depends(self))
  File "C:\odoo18\server\odoo\fields.py", line 834, in resolve_depends
    warnings.warn(
 
2025-07-28 22:06:03,801 200164 INFO moassLive25_2 odoo.modules.loading: Modules loaded. 
2025-07-28 22:06:03,813 200164 INFO moassLive25_2 odoo.modules.registry: Registry changed, signaling through the database 
2025-07-28 22:06:03,814 200164 INFO moassLive25_2 odoo.modules.registry: Registry loaded in 18.623s 
2025-07-28 22:07:02,160 189216 INFO ? odoo: Odoo version 18.0-20241204 
2025-07-28 22:07:02,160 189216 INFO ? odoo: Using configuration file at C:\odoo18\server\odoo.conf 
2025-07-28 22:07:02,160 189216 INFO ? odoo: addons paths: ['C:\\odoo18\\server\\odoo\\addons', 'c:\\odoo18\\sessions\\addons\\18.0', 'c:\\odoo18\\server\\enterprise183', 'c:\\odoo18\\server\\custom_moass_live_5', 'c:\\odoo18\\server\\odoo\\addons'] 
2025-07-28 22:07:02,160 189216 INFO ? odoo: database: openpg@localhost:5432 
2025-07-28 22:07:02,508 189216 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo18\thirdparty\wkhtmltopdf.exe 
2025-07-28 22:07:02,532 189216 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-07-28 22:07:04,258 189216 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8090 
2025-07-28 22:07:04,331 189216 INFO moassLive25_2 odoo.modules.loading: loading 1 modules... 
2025-07-28 22:07:04,382 189216 INFO moassLive25_2 odoo.modules.loading: 1 modules loaded in 0.05s, 0 queries (+0 extra) 
2025-07-28 22:07:04,404 189216 INFO moassLive25_2 odoo.modules.loading: updating modules list 
2025-07-28 22:07:04,409 189216 INFO moassLive25_2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-28 22:07:04,711 189216 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'app_api', defaulting to LGPL-3 
2025-07-28 22:07:04,847 189216 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'branch_cost', defaulting to LGPL-3 
2025-07-28 22:07:04,947 189216 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'cubes_move_restrictions', defaulting to LGPL-3 
2025-07-28 22:07:05,223 189216 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-07-28 22:07:08,072 189216 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'payment_reports_custom', defaulting to LGPL-3 
2025-07-28 22:07:08,545 189216 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'reconciliation_report', defaulting to LGPL-3 
2025-07-28 22:07:09,800 189216 INFO moassLive25_2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['Price Checker Kiosk'] to user __system__ #1 via n/a 
2025-07-28 22:07:09,800 189216 INFO moassLive25_2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['Price Checker Kiosk'] to user __system__ #1 via n/a 
2025-07-28 22:07:12,448 189216 INFO moassLive25_2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-28 22:07:12,500 189216 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.users', <function Users.context_get at 0x0000023E48E24180>, 1)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x0000023E48D3DDA0>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-07-28 22:07:12,532 189216 INFO moassLive25_2 odoo.modules.loading: loading 251 modules... 
2025-07-28 22:07:12,547 189216 WARNING moassLive25_2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-28 22:07:13,032 189216 INFO moassLive25_2 odoo.modules.loading: Loading module price_checker_kiosk (55/251) 
2025-07-28 22:07:13,139 189216 INFO moassLive25_2 odoo.modules.loading: loading price_checker_kiosk/security/ir.model.access.csv 
2025-07-28 22:07:13,143 189216 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x0000023E48D3DDA0>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-07-28 22:07:13,394 189216 INFO moassLive25_2 odoo.modules.loading: loading price_checker_kiosk/views/price_checker_templates.xml 
2025-07-28 22:07:13,420 189216 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x0000023E48D3DDA0>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-07-28 22:07:13,428 189216 INFO moassLive25_2 odoo.addons.base.models.ir_module: module price_checker_kiosk: no translation for language ar_001 
2025-07-28 22:07:13,441 189216 INFO moassLive25_2 odoo.modules.loading: Module price_checker_kiosk loaded in 0.41s, 49 queries (+49 other) 
2025-07-28 22:07:14,389 189216 WARNING moassLive25_2 py.warnings: C:\odoo18\server\odoo\api.py:466: DeprecationWarning: The model odoo.addons.branch_management.models.purchase is not overriding the create method in batch
  File "C:\odoo18\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\odoo18\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\odoo18\server\odoo\cli\server.py", line 180, in run
    main(args)
  File "C:\odoo18\server\odoo\cli\server.py", line 173, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\odoo18\server\odoo\service\server.py", line 1402, in start
    rc = server.run(preload, stop)
  File "C:\odoo18\server\odoo\service\server.py", line 577, in run
    rc = preload_registries(preload)
  File "C:\odoo18\server\odoo\service\server.py", line 1306, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18\server\odoo\modules\loading.py", line 476, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\odoo18\server\odoo\modules\loading.py", line 364, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\odoo18\server\odoo\modules\loading.py", line 185, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo18\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "c:\odoo18\server\custom_moass_live_5\branch_management\__init__.py", line 1, in <module>
    from . import models
  File "c:\odoo18\server\custom_moass_live_5\branch_management\models\__init__.py", line 6, in <module>
    from . import purchase
  File "c:\odoo18\server\custom_moass_live_5\branch_management\models\purchase.py", line 3, in <module>
    class PurchaseOrder(models.Model):
  File "c:\odoo18\server\custom_moass_live_5\branch_management\models\purchase.py", line 72, in PurchaseOrder
    @api.model
  File "C:\odoo18\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\odoo18\server\odoo\api.py", line 466, in model_create_single
    warnings.warn(
 
2025-07-28 22:07:14,691 189216 WARNING moassLive25_2 odoo.models: The model notification.center has no _description 
2025-07-28 22:07:14,814 189216 WARNING moassLive25_2 odoo.models: The model location.stock.wizard has no _description 
2025-07-28 22:07:14,876 189216 INFO moassLive25_2 odoo.modules.loading: 251 modules loaded in 2.34s, 49 queries (+49 extra) 
2025-07-28 22:07:14,880 189216 INFO moassLive25_2 odoo.modules.loading: loading 251 modules... 
2025-07-28 22:07:14,880 189216 INFO moassLive25_2 odoo.modules.loading: 251 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-28 22:07:14,883 189216 INFO moassLive25_2 odoo.modules.loading: loading 251 modules... 
2025-07-28 22:07:14,884 189216 INFO moassLive25_2 odoo.modules.loading: 251 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-28 22:07:15,015 189216 WARNING moassLive25_2 odoo.fields: Field vip.transfer.partner_id: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:07:15,015 189216 WARNING moassLive25_2 odoo.fields: Field vip.transfer.source_warehouse_id: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:07:15,015 189216 WARNING moassLive25_2 odoo.fields: Field vip.transfer.state: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:07:15,016 189216 WARNING moassLive25_2 odoo.fields: Field notification.center.notification_type: unknown parameter 'states', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:07:15,016 189216 WARNING moassLive25_2 odoo.fields: Field notification.center.user_ids: unknown parameter 'states', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:07:15,016 189216 WARNING moassLive25_2 odoo.fields: Field branch.cost.report.branch_id with unknown comodel_name 'res.branch' 
2025-07-28 22:07:15,016 189216 WARNING moassLive25_2 odoo.fields: Field branch.transfer.state: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:07:15,250 189216 WARNING moassLive25_2 odoo.fields: Field vip.transfer.partner_id: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:07:15,250 189216 WARNING moassLive25_2 odoo.fields: Field vip.transfer.source_warehouse_id: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:07:15,250 189216 WARNING moassLive25_2 odoo.fields: Field vip.transfer.state: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:07:15,250 189216 WARNING moassLive25_2 odoo.fields: Field notification.center.notification_type: unknown parameter 'states', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:07:15,250 189216 WARNING moassLive25_2 odoo.fields: Field notification.center.user_ids: unknown parameter 'states', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:07:15,250 189216 WARNING moassLive25_2 odoo.fields: Field branch.transfer.state: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:07:15,290 189216 INFO moassLive25_2 odoo.modules.loading: Model account.followup.report.handler is declared but cannot be loaded! (Perhaps a module was partially removed or renamed) 
2025-07-28 22:07:15,357 189216 INFO moassLive25_2 odoo.modules.loading: Model account.customer.statement.report.handler is declared but cannot be loaded! (Perhaps a module was partially removed or renamed) 
2025-07-28 22:07:15,938 189216 WARNING moassLive25_2 py.warnings: C:\odoo18\server\odoo\fields.py:834: UserWarning: Field 'partner.ledger.line.env_ref_lines' in dependency of partner.ledger.line.ending_balance should be searchable. This is necessary to determine which records to recompute when partner.ledger.line.balance is modified. You should either make the field searchable, or simplify the field dependency.
  File "C:\odoo18\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\odoo18\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\odoo18\server\odoo\cli\server.py", line 180, in run
    main(args)
  File "C:\odoo18\server\odoo\cli\server.py", line 173, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\odoo18\server\odoo\service\server.py", line 1402, in start
    rc = server.run(preload, stop)
  File "C:\odoo18\server\odoo\service\server.py", line 577, in run
    rc = preload_registries(preload)
  File "C:\odoo18\server\odoo\service\server.py", line 1306, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18\server\odoo\modules\loading.py", line 583, in load_modules
    env['res.groups']._update_user_groups_view()
  File "C:\odoo18\server\odoo\addons\base\models\res_users.py", line 1794, in _update_user_groups_view
    view.with_context(new_context).write({'arch': xml_content})
  File "c:\odoo18\server\enterprise183\web_studio\models\studio_mixin.py", line 33, in write
    res = super(StudioMixin, self).write(vals)
  File "C:\odoo18\server\odoo\addons\base\models\ir_ui_view.py", line 537, in write
    res = super(View, self).write(self._compute_defaults(vals))
  File "C:\odoo18\server\odoo\models.py", line 4677, in write
    if self.pool.is_modifying_relations(field):
  File "C:\odoo18\server\odoo\modules\registry.py", line 533, in is_modifying_relations
    result = field in self._field_triggers and (
  File "C:\odoo18\server\odoo\tools\func.py", line 42, in __get__
    value = self.fget(obj)
  File "C:\odoo18\server\odoo\modules\registry.py", line 514, in _field_triggers
    dependencies = list(field.resolve_depends(self))
  File "C:\odoo18\server\odoo\fields.py", line 834, in resolve_depends
    warnings.warn(
 
2025-07-28 22:07:18,015 189216 INFO moassLive25_2 odoo.modules.loading: Modules loaded. 
2025-07-28 22:07:18,031 189216 INFO moassLive25_2 odoo.modules.registry: Registry changed, signaling through the database 
2025-07-28 22:07:18,031 189216 INFO moassLive25_2 odoo.modules.registry: Registry loaded in 13.770s 
2025-07-28 22:07:54,346 189216 INFO moassLive25_2 odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-07-28 22:07:54,380 189216 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x0000023E48D3DDA0>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-07-28 22:07:54,617 189216 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\25/25eab71cf1a7250397d5f1b39bbdb69315983ba2 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023E484E02C0>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\25/25eab71cf1a7250397d5f1b39bbdb69315983ba2'
2025-07-28 22:07:54,618 189216 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\d9/d91cb0023dd0de9111309aa17eddcd5caff2fa91 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023E484E02C0>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\d9/d91cb0023dd0de9111309aa17eddcd5caff2fa91'
2025-07-28 22:07:54,618 189216 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\ca/cad9804f210a8c1e26c8ebab07b8056b9d5ec66b 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023E484E02C0>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\ca/cad9804f210a8c1e26c8ebab07b8056b9d5ec66b'
2025-07-28 22:07:54,619 189216 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\70/70633333ff0c9230f06ebf2b3bf7770df6d800d0 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023E484E02C0>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\70/70633333ff0c9230f06ebf2b3bf7770df6d800d0'
2025-07-28 22:07:54,619 189216 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\37/3709cc2e0d5df4c199f96c6d07e2aaf29a63bcba 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023E484E02C0>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\37/3709cc2e0d5df4c199f96c6d07e2aaf29a63bcba'
2025-07-28 22:07:54,619 189216 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\c2/c21721ec3ec2e8d086eebf804e0ede5f25aa14d7 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023E484E02C0>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\c2/c21721ec3ec2e8d086eebf804e0ede5f25aa14d7'
2025-07-28 22:07:54,619 189216 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\f9/f9cf2c8b67d3ebc89ca4d180ef207a248c4e3130 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023E484E02C0>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\f9/f9cf2c8b67d3ebc89ca4d180ef207a248c4e3130'
2025-07-28 22:07:54,619 189216 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\7d/7d36617149ed2fe93e9ba984cffe16f468bd7a0e 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023E484E02C0>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\7d/7d36617149ed2fe93e9ba984cffe16f468bd7a0e'
2025-07-28 22:07:54,621 189216 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\c2/c2f49a19f3cf050243e62b22cde5072cca84fcaa 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023E484E02C0>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\c2/c2f49a19f3cf050243e62b22cde5072cca84fcaa'
2025-07-28 22:07:54,621 189216 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\67/670e9ff9033932ebe066245e9ac5c3382cde45f4 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023E484E02C0>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\67/670e9ff9033932ebe066245e9ac5c3382cde45f4'
2025-07-28 22:07:54,622 189216 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\51/517a97a04c19cdd67572ff8a0ef89fad5a2c278e 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023E484E02C0>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\51/517a97a04c19cdd67572ff8a0ef89fad5a2c278e'
2025-07-28 22:07:54,622 189216 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\1c/1c96f0109a19f3ff4c0a7486431a79da8c42d8de 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023E484E02C0>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\1c/1c96f0109a19f3ff4c0a7486431a79da8c42d8de'
2025-07-28 22:07:54,622 189216 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\50/503be145dcd1c8f6491ae855820d93b8ab8363da 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023E484E02C0>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\50/503be145dcd1c8f6491ae855820d93b8ab8363da'
2025-07-28 22:07:54,623 189216 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\1c/1c16002d10f6155661a6ae83417fee29e281fb7b 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023E484E02C0>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\1c/1c16002d10f6155661a6ae83417fee29e281fb7b'
2025-07-28 22:07:54,623 189216 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\a4/a4511b76524b49a86d44d27508dd20f056bfe827 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023E484E02C0>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\a4/a4511b76524b49a86d44d27508dd20f056bfe827'
2025-07-28 22:07:54,623 189216 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\f3/f31bdf173e5748b875ca5e389e1edba54b4bff25 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023E484E02C0>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\f3/f31bdf173e5748b875ca5e389e1edba54b4bff25'
2025-07-28 22:07:54,624 189216 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\74/744fc247b813c44f4cf02357706e88b283cb0f97 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023E484E02C0>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\74/744fc247b813c44f4cf02357706e88b283cb0f97'
2025-07-28 22:07:54,624 189216 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\ac/ac2a935c445b4a44ecb11e8b692d41661f9ecae1 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023E484E02C0>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\ac/ac2a935c445b4a44ecb11e8b692d41661f9ecae1'
2025-07-28 22:07:54,624 189216 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\3d/3d7a7360fe4a73a90dd3b4ba554a9254ed61e864 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023E484E02C0>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\3d/3d7a7360fe4a73a90dd3b4ba554a9254ed61e864'
2025-07-28 22:07:54,625 189216 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\b7/b73a9deb85def8a1a00e4d545c4f79747d78f487 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023E484E02C0>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\b7/b73a9deb85def8a1a00e4d545c4f79747d78f487'
2025-07-28 22:07:54,683 189216 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'payment_reports_custom', defaulting to LGPL-3 
2025-07-28 22:07:54,699 189216 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'app_api', defaulting to LGPL-3 
2025-07-28 22:07:54,714 189216 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'branch_cost', defaulting to LGPL-3 
2025-07-28 22:07:54,717 189216 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-07-28 22:07:54,745 189216 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'reconciliation_report', defaulting to LGPL-3 
2025-07-28 22:07:54,771 189216 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'cubes_move_restrictions', defaulting to LGPL-3 
2025-07-28 22:07:55,469 189216 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:07:55] "GET /odoo HTTP/1.1" 200 - 132 0.143 0.984
2025-07-28 22:07:59,439 189216 INFO moassLive25_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/95d82fc/web.assets_web.min.css (id:13286) 
2025-07-28 22:07:59,442 189216 INFO moassLive25_2 odoo.addons.base.models.assetsbundle: Deleting attachments [13281] (matching /web/assets/_______/web.assets_web.min.css) because it was replaced with /web/assets/95d82fc/web.assets_web.min.css 
2025-07-28 22:07:59,629 189216 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:07:59] "GET /web/assets/95d82fc/web.assets_web.min.css HTTP/1.1" 200 - 17 0.217 3.934
2025-07-28 22:08:05,479 189216 INFO moassLive25_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/90c701f/web.assets_web_print.min.css (id:13287) 
2025-07-28 22:08:05,484 189216 INFO moassLive25_2 odoo.addons.base.models.assetsbundle: Deleting attachments [13283] (matching /web/assets/_______/web.assets_web_print.min.css) because it was replaced with /web/assets/90c701f/web.assets_web_print.min.css 
2025-07-28 22:08:05,546 189216 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:08:05] "GET /bus/websocket_worker_bundle?v=18.0-2 HTTP/1.1" 304 - 4 0.018 0.171
2025-07-28 22:08:05,567 189216 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:08:05] "POST /hr_attendance/attendance_user_data HTTP/1.1" 200 - 8 0.041 0.203
2025-07-28 22:08:05,581 189216 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\10/10b00a9b7504892eb1bca5d9b6c2848842d67ea6 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\10/10b00a9b7504892eb1bca5d9b6c2848842d67ea6'
2025-07-28 22:08:05,585 189216 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:08:05] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 16 0.050 0.181
2025-07-28 22:08:05,600 189216 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:08:05] "GET /web/assets/90c701f/web.assets_web_print.min.css HTTP/1.1" 200 - 10 0.284 9.666
2025-07-28 22:08:05,685 189216 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:08:05] "POST /mail/data HTTP/1.1" 200 - 46 0.132 0.200
2025-07-28 22:08:05,705 189216 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:08:05] "GET /price_checker HTTP/1.1" 200 - 38 0.075 6.386
2025-07-28 22:08:05,885 189216 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:08:05] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 1 0.001 0.011
2025-07-28 22:08:09,601 189216 INFO ? werkzeug: 127.0.0.1 - - [28/Jul/2025 22:08:09] "GET /price_checker_kiosk/static/src/img/cubes_logo.png HTTP/1.1" 200 - 0 0.000 0.017
2025-07-28 22:08:09,614 189216 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:08:09] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.003 0.022
2025-07-28 22:08:09,631 189216 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:08:09] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.032 0.016
2025-07-28 22:08:09,636 189216 INFO moassLive25_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/ea65053/web.assets_frontend.min.css (id:13288) 
2025-07-28 22:08:09,638 189216 INFO moassLive25_2 odoo.addons.base.models.assetsbundle: Deleting attachments [13282] (matching /web/assets/_______/web.assets_frontend.min.css) because it was replaced with /web/assets/ea65053/web.assets_frontend.min.css 
2025-07-28 22:08:09,640 189216 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-07-28 22:08:09,668 189216 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:08:09] "GET /web/assets/ea65053/web.assets_frontend.min.css HTTP/1.1" 200 - 10 0.030 3.690
2025-07-28 22:08:37,250 189216 INFO moassLive25_2 odoo.addons.price_checker_kiosk.controllers.main: Searching for barcode: 6253016805365 
2025-07-28 22:08:37,375 189216 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:08:37] "POST /price_checker/search HTTP/1.1" 200 - 12 0.078 0.054
2025-07-28 22:08:50,020 189216 INFO moassLive25_2 odoo.addons.price_checker_kiosk.controllers.main: Searching for barcode: 6253016805365 
2025-07-28 22:08:50,037 189216 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:08:50] "POST /price_checker/search HTTP/1.1" 200 - 10 0.008 0.012
2025-07-28 22:13:40,750 153376 INFO ? odoo: Odoo version 18.0-20241204 
2025-07-28 22:13:40,751 153376 INFO ? odoo: Using configuration file at C:\odoo18\server\odoo.conf 
2025-07-28 22:13:40,751 153376 INFO ? odoo: addons paths: ['C:\\odoo18\\server\\odoo\\addons', 'c:\\odoo18\\sessions\\addons\\18.0', 'c:\\odoo18\\server\\enterprise183', 'c:\\odoo18\\server\\custom_moass_live_5', 'c:\\odoo18\\server\\odoo\\addons'] 
2025-07-28 22:13:40,751 153376 INFO ? odoo: database: openpg@localhost:5432 
2025-07-28 22:13:40,875 153376 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo18\thirdparty\wkhtmltopdf.exe 
2025-07-28 22:13:40,895 153376 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-07-28 22:13:42,426 153376 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8090 
2025-07-28 22:13:42,609 153376 INFO moassLive25_2 odoo.modules.loading: loading 1 modules... 
2025-07-28 22:13:42,637 153376 INFO moassLive25_2 odoo.modules.loading: 1 modules loaded in 0.03s, 0 queries (+0 extra) 
2025-07-28 22:13:42,650 153376 INFO moassLive25_2 odoo.modules.loading: updating modules list 
2025-07-28 22:13:42,654 153376 INFO moassLive25_2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-28 22:13:42,928 153376 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'app_api', defaulting to LGPL-3 
2025-07-28 22:13:43,081 153376 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'branch_cost', defaulting to LGPL-3 
2025-07-28 22:13:43,123 153376 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'cubes_move_restrictions', defaulting to LGPL-3 
2025-07-28 22:13:43,216 153376 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-07-28 22:13:44,927 153376 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'payment_reports_custom', defaulting to LGPL-3 
2025-07-28 22:13:45,258 153376 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'reconciliation_report', defaulting to LGPL-3 
2025-07-28 22:13:46,256 153376 INFO moassLive25_2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['Price Checker Kiosk'] to user __system__ #1 via n/a 
2025-07-28 22:13:46,257 153376 INFO moassLive25_2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['Price Checker Kiosk'] to user __system__ #1 via n/a 
2025-07-28 22:13:49,440 153376 INFO moassLive25_2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-28 22:13:49,502 153376 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.users', <function Users.context_get at 0x00000164A40A9940>, 1)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x00000164A3FCF560>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-07-28 22:13:49,563 153376 INFO moassLive25_2 odoo.modules.loading: loading 251 modules... 
2025-07-28 22:13:49,589 153376 WARNING moassLive25_2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-28 22:13:50,382 153376 INFO moassLive25_2 odoo.modules.loading: Loading module price_checker_kiosk (55/251) 
2025-07-28 22:13:50,541 153376 INFO moassLive25_2 odoo.modules.loading: loading price_checker_kiosk/security/ir.model.access.csv 
2025-07-28 22:13:50,543 153376 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x00000164A3FCF560>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-07-28 22:13:50,927 153376 INFO moassLive25_2 odoo.modules.loading: loading price_checker_kiosk/views/price_checker_templates.xml 
2025-07-28 22:13:50,957 153376 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x00000164A3FCF560>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-07-28 22:13:50,967 153376 INFO moassLive25_2 odoo.addons.base.models.ir_module: module price_checker_kiosk: no translation for language ar_001 
2025-07-28 22:13:50,985 153376 INFO moassLive25_2 odoo.modules.loading: Module price_checker_kiosk loaded in 0.60s, 49 queries (+49 other) 
2025-07-28 22:13:52,260 153376 WARNING moassLive25_2 py.warnings: C:\odoo18\server\odoo\api.py:466: DeprecationWarning: The model odoo.addons.branch_management.models.purchase is not overriding the create method in batch
  File "C:\odoo18\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\odoo18\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\odoo18\server\odoo\cli\server.py", line 180, in run
    main(args)
  File "C:\odoo18\server\odoo\cli\server.py", line 173, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\odoo18\server\odoo\service\server.py", line 1402, in start
    rc = server.run(preload, stop)
  File "C:\odoo18\server\odoo\service\server.py", line 577, in run
    rc = preload_registries(preload)
  File "C:\odoo18\server\odoo\service\server.py", line 1306, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18\server\odoo\modules\loading.py", line 476, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\odoo18\server\odoo\modules\loading.py", line 364, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\odoo18\server\odoo\modules\loading.py", line 185, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo18\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "c:\odoo18\server\custom_moass_live_5\branch_management\__init__.py", line 1, in <module>
    from . import models
  File "c:\odoo18\server\custom_moass_live_5\branch_management\models\__init__.py", line 6, in <module>
    from . import purchase
  File "c:\odoo18\server\custom_moass_live_5\branch_management\models\purchase.py", line 3, in <module>
    class PurchaseOrder(models.Model):
  File "c:\odoo18\server\custom_moass_live_5\branch_management\models\purchase.py", line 72, in PurchaseOrder
    @api.model
  File "C:\odoo18\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\odoo18\server\odoo\api.py", line 466, in model_create_single
    warnings.warn(
 
2025-07-28 22:13:52,513 153376 WARNING moassLive25_2 odoo.models: The model notification.center has no _description 
2025-07-28 22:13:52,584 153376 WARNING moassLive25_2 odoo.models: The model location.stock.wizard has no _description 
2025-07-28 22:13:52,616 153376 INFO moassLive25_2 odoo.modules.loading: 251 modules loaded in 3.05s, 49 queries (+49 extra) 
2025-07-28 22:13:52,619 153376 INFO moassLive25_2 odoo.modules.loading: loading 251 modules... 
2025-07-28 22:13:52,620 153376 INFO moassLive25_2 odoo.modules.loading: 251 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-28 22:13:52,622 153376 INFO moassLive25_2 odoo.modules.loading: loading 251 modules... 
2025-07-28 22:13:52,622 153376 INFO moassLive25_2 odoo.modules.loading: 251 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-28 22:13:52,955 153376 WARNING moassLive25_2 odoo.fields: Field vip.transfer.partner_id: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:13:52,955 153376 WARNING moassLive25_2 odoo.fields: Field vip.transfer.source_warehouse_id: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:13:52,955 153376 WARNING moassLive25_2 odoo.fields: Field vip.transfer.state: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:13:52,956 153376 WARNING moassLive25_2 odoo.fields: Field notification.center.notification_type: unknown parameter 'states', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:13:52,956 153376 WARNING moassLive25_2 odoo.fields: Field notification.center.user_ids: unknown parameter 'states', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:13:52,956 153376 WARNING moassLive25_2 odoo.fields: Field branch.cost.report.branch_id with unknown comodel_name 'res.branch' 
2025-07-28 22:13:52,956 153376 WARNING moassLive25_2 odoo.fields: Field branch.transfer.state: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:13:53,315 153376 WARNING moassLive25_2 odoo.fields: Field vip.transfer.partner_id: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:13:53,316 153376 WARNING moassLive25_2 odoo.fields: Field vip.transfer.source_warehouse_id: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:13:53,316 153376 WARNING moassLive25_2 odoo.fields: Field vip.transfer.state: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:13:53,316 153376 WARNING moassLive25_2 odoo.fields: Field notification.center.notification_type: unknown parameter 'states', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:13:53,316 153376 WARNING moassLive25_2 odoo.fields: Field notification.center.user_ids: unknown parameter 'states', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:13:53,316 153376 WARNING moassLive25_2 odoo.fields: Field branch.transfer.state: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:13:53,387 153376 INFO moassLive25_2 odoo.modules.loading: Model account.followup.report.handler is declared but cannot be loaded! (Perhaps a module was partially removed or renamed) 
2025-07-28 22:13:53,431 153376 INFO moassLive25_2 odoo.modules.loading: Model account.customer.statement.report.handler is declared but cannot be loaded! (Perhaps a module was partially removed or renamed) 
2025-07-28 22:13:54,117 153376 WARNING moassLive25_2 py.warnings: C:\odoo18\server\odoo\fields.py:834: UserWarning: Field 'partner.ledger.line.env_ref_lines' in dependency of partner.ledger.line.ending_balance should be searchable. This is necessary to determine which records to recompute when partner.ledger.line.balance is modified. You should either make the field searchable, or simplify the field dependency.
  File "C:\odoo18\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\odoo18\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\odoo18\server\odoo\cli\server.py", line 180, in run
    main(args)
  File "C:\odoo18\server\odoo\cli\server.py", line 173, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\odoo18\server\odoo\service\server.py", line 1402, in start
    rc = server.run(preload, stop)
  File "C:\odoo18\server\odoo\service\server.py", line 577, in run
    rc = preload_registries(preload)
  File "C:\odoo18\server\odoo\service\server.py", line 1306, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18\server\odoo\modules\loading.py", line 583, in load_modules
    env['res.groups']._update_user_groups_view()
  File "C:\odoo18\server\odoo\addons\base\models\res_users.py", line 1794, in _update_user_groups_view
    view.with_context(new_context).write({'arch': xml_content})
  File "c:\odoo18\server\enterprise183\web_studio\models\studio_mixin.py", line 33, in write
    res = super(StudioMixin, self).write(vals)
  File "C:\odoo18\server\odoo\addons\base\models\ir_ui_view.py", line 537, in write
    res = super(View, self).write(self._compute_defaults(vals))
  File "C:\odoo18\server\odoo\models.py", line 4677, in write
    if self.pool.is_modifying_relations(field):
  File "C:\odoo18\server\odoo\modules\registry.py", line 533, in is_modifying_relations
    result = field in self._field_triggers and (
  File "C:\odoo18\server\odoo\tools\func.py", line 42, in __get__
    value = self.fget(obj)
  File "C:\odoo18\server\odoo\modules\registry.py", line 514, in _field_triggers
    dependencies = list(field.resolve_depends(self))
  File "C:\odoo18\server\odoo\fields.py", line 834, in resolve_depends
    warnings.warn(
 
2025-07-28 22:13:55,901 153376 INFO moassLive25_2 odoo.modules.loading: Modules loaded. 
2025-07-28 22:13:55,916 153376 INFO moassLive25_2 odoo.modules.registry: Registry changed, signaling through the database 
2025-07-28 22:13:55,918 153376 INFO moassLive25_2 odoo.modules.registry: Registry loaded in 13.480s 
2025-07-28 22:13:55,920 153376 INFO moassLive25_2 odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-07-28 22:13:55,972 153376 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x00000164A3FCF560>, 'url_code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x00000164A3FCF560>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-07-28 22:13:56,056 153376 INFO moassLive25_2 odoo.addons.base.models.res_device: User 2 inserts device log (Z73-Ci40EzibQIr3IEdWPd8vycE2dH1q5ynMFALKk0) 
2025-07-28 22:13:56,056 153376 INFO moassLive25_2 odoo.addons.base.models.res_device: User 2 inserts device log (Z73-Ci40EzibQIr3IEdWPd8vycE2dH1q5ynMFALKk0) 
2025-07-28 22:13:56,065 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:13:56] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 5 0.018 10.311
2025-07-28 22:13:56,131 153376 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'app_api', defaulting to LGPL-3 
2025-07-28 22:13:56,144 153376 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'cubes_move_restrictions', defaulting to LGPL-3 
2025-07-28 22:13:56,145 153376 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'reconciliation_report', defaulting to LGPL-3 
2025-07-28 22:13:56,166 153376 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'payment_reports_custom', defaulting to LGPL-3 
2025-07-28 22:13:56,183 153376 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'branch_cost', defaulting to LGPL-3 
2025-07-28 22:13:56,195 153376 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-07-28 22:13:56,466 153376 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-07-28 22:13:56,527 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:13:56] "GET /price_checker HTTP/1.1" 200 - 55 0.087 13.935
2025-07-28 22:13:58,115 153376 INFO moassLive25_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/5160663/web.assets_frontend_lazy.min.js (id:13289) 
2025-07-28 22:13:58,117 153376 INFO moassLive25_2 odoo.addons.base.models.assetsbundle: Deleting attachments [13284] (matching /web/assets/_______/web.assets_frontend_lazy.min.js) because it was replaced with /web/assets/5160663/web.assets_frontend_lazy.min.js 
2025-07-28 22:13:58,309 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:13:58] "GET /web/assets/5160663/web.assets_frontend_lazy.min.js HTTP/1.1" 200 - 13 0.057 1.671
2025-07-28 22:14:29,968 153376 INFO moassLive25_2 odoo.addons.price_checker_kiosk.controllers.main: Searching for barcode: 6253016805365 
2025-07-28 22:14:30,085 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:14:30] "POST /price_checker/search HTTP/1.1" 200 - 11 0.077 0.049
2025-07-28 22:14:38,933 153376 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\25/25eab71cf1a7250397d5f1b39bbdb69315983ba2 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000164A3719A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\25/25eab71cf1a7250397d5f1b39bbdb69315983ba2'
2025-07-28 22:14:38,934 153376 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\d9/d91cb0023dd0de9111309aa17eddcd5caff2fa91 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000164A3719A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\d9/d91cb0023dd0de9111309aa17eddcd5caff2fa91'
2025-07-28 22:14:38,938 153376 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\ca/cad9804f210a8c1e26c8ebab07b8056b9d5ec66b 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000164A3719A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\ca/cad9804f210a8c1e26c8ebab07b8056b9d5ec66b'
2025-07-28 22:14:38,939 153376 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\70/70633333ff0c9230f06ebf2b3bf7770df6d800d0 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000164A3719A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\70/70633333ff0c9230f06ebf2b3bf7770df6d800d0'
2025-07-28 22:14:38,942 153376 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\37/3709cc2e0d5df4c199f96c6d07e2aaf29a63bcba 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000164A3719A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\37/3709cc2e0d5df4c199f96c6d07e2aaf29a63bcba'
2025-07-28 22:14:38,944 153376 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\c2/c21721ec3ec2e8d086eebf804e0ede5f25aa14d7 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000164A3719A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\c2/c21721ec3ec2e8d086eebf804e0ede5f25aa14d7'
2025-07-28 22:14:38,945 153376 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\f9/f9cf2c8b67d3ebc89ca4d180ef207a248c4e3130 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000164A3719A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\f9/f9cf2c8b67d3ebc89ca4d180ef207a248c4e3130'
2025-07-28 22:14:38,946 153376 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\7d/7d36617149ed2fe93e9ba984cffe16f468bd7a0e 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000164A3719A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\7d/7d36617149ed2fe93e9ba984cffe16f468bd7a0e'
2025-07-28 22:14:38,947 153376 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\c2/c2f49a19f3cf050243e62b22cde5072cca84fcaa 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000164A3719A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\c2/c2f49a19f3cf050243e62b22cde5072cca84fcaa'
2025-07-28 22:14:38,948 153376 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\67/670e9ff9033932ebe066245e9ac5c3382cde45f4 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000164A3719A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\67/670e9ff9033932ebe066245e9ac5c3382cde45f4'
2025-07-28 22:14:38,950 153376 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\51/517a97a04c19cdd67572ff8a0ef89fad5a2c278e 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000164A3719A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\51/517a97a04c19cdd67572ff8a0ef89fad5a2c278e'
2025-07-28 22:14:38,951 153376 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\1c/1c96f0109a19f3ff4c0a7486431a79da8c42d8de 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000164A3719A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\1c/1c96f0109a19f3ff4c0a7486431a79da8c42d8de'
2025-07-28 22:14:38,953 153376 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\50/503be145dcd1c8f6491ae855820d93b8ab8363da 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000164A3719A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\50/503be145dcd1c8f6491ae855820d93b8ab8363da'
2025-07-28 22:14:38,956 153376 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\1c/1c16002d10f6155661a6ae83417fee29e281fb7b 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000164A3719A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\1c/1c16002d10f6155661a6ae83417fee29e281fb7b'
2025-07-28 22:14:38,958 153376 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\a4/a4511b76524b49a86d44d27508dd20f056bfe827 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000164A3719A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\a4/a4511b76524b49a86d44d27508dd20f056bfe827'
2025-07-28 22:14:38,960 153376 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\f3/f31bdf173e5748b875ca5e389e1edba54b4bff25 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000164A3719A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\f3/f31bdf173e5748b875ca5e389e1edba54b4bff25'
2025-07-28 22:14:38,962 153376 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\74/744fc247b813c44f4cf02357706e88b283cb0f97 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000164A3719A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\74/744fc247b813c44f4cf02357706e88b283cb0f97'
2025-07-28 22:14:38,964 153376 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\ac/ac2a935c445b4a44ecb11e8b692d41661f9ecae1 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000164A3719A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\ac/ac2a935c445b4a44ecb11e8b692d41661f9ecae1'
2025-07-28 22:14:38,966 153376 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\3d/3d7a7360fe4a73a90dd3b4ba554a9254ed61e864 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000164A3719A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\3d/3d7a7360fe4a73a90dd3b4ba554a9254ed61e864'
2025-07-28 22:14:38,967 153376 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\b7/b73a9deb85def8a1a00e4d545c4f79747d78f487 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000164A3719A80>, 2, '1', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\b7/b73a9deb85def8a1a00e4d545c4f79747d78f487'
2025-07-28 22:14:40,636 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:14:40] "GET /odoo HTTP/1.1" 200 - 116 0.140 1.879
2025-07-28 22:14:45,942 153376 INFO moassLive25_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/bcb85ea/web.assets_web.min.js (id:13290) 
2025-07-28 22:14:45,944 153376 INFO moassLive25_2 odoo.addons.base.models.assetsbundle: Deleting attachments [13285] (matching /web/assets/_______/web.assets_web.min.js) because it was replaced with /web/assets/bcb85ea/web.assets_web.min.js 
2025-07-28 22:14:46,501 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:14:46] "GET /web/assets/bcb85ea/web.assets_web.min.js HTTP/1.1" 200 - 13 0.134 5.709
2025-07-28 22:14:47,025 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:14:47] "POST /hr_attendance/attendance_user_data HTTP/1.1" 200 - 8 0.008 0.012
2025-07-28 22:14:47,172 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:14:47] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.036 0.080
2025-07-28 22:14:47,174 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:14:47] "GET /bus/websocket_worker_bundle?v=18.0-2 HTTP/1.1" 304 - 4 0.032 0.084
2025-07-28 22:14:47,209 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:14:47] "POST /mail/data HTTP/1.1" 200 - 46 0.105 0.077
2025-07-28 22:14:47,343 153376 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\10/10b00a9b7504892eb1bca5d9b6c2848842d67ea6 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\10/10b00a9b7504892eb1bca5d9b6c2848842d67ea6'
2025-07-28 22:14:47,346 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:14:47] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.009 0.015
2025-07-28 22:14:47,508 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:14:47] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 1 0.001 0.007
2025-07-28 22:14:49,034 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:14:49] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.003
2025-07-28 22:15:31,458 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:15:31] "GET /price_checker HTTP/1.1" 200 - 4 0.004 0.021
2025-07-28 22:15:35,532 153376 INFO moassLive25_2 odoo.addons.price_checker_kiosk.controllers.main: Searching for barcode: 6253016805365 
2025-07-28 22:15:35,557 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:15:35] "POST /price_checker/search HTTP/1.1" 200 - 10 0.011 0.025
2025-07-28 22:17:38,663 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:17:38] "GET /price_checker HTTP/1.1" 200 - 9 0.008 0.011
2025-07-28 22:17:38,861 153376 INFO ? werkzeug: 127.0.0.1 - - [28/Jul/2025 22:17:38] "GET /price_checker_kiosk/static/src/css/price_checker.css HTTP/1.1" 200 - 0 0.000 0.021
2025-07-28 22:17:38,868 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:17:38] "GET /web/assets/ea65053/web.assets_frontend.min.css HTTP/1.1" 200 - 7 0.019 0.029
2025-07-28 22:17:38,885 153376 INFO ? werkzeug: 127.0.0.1 - - [28/Jul/2025 22:17:38] "GET /price_checker_kiosk/static/src/img/cubes_logo.png HTTP/1.1" 200 - 0 0.000 0.005
2025-07-28 22:17:38,912 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:17:38] "GET /web/assets/aaef59a/web.assets_frontend_minimal.min.js HTTP/1.1" 200 - 7 0.046 0.033
2025-07-28 22:17:39,205 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:17:39] "GET /web/assets/5160663/web.assets_frontend_lazy.min.js HTTP/1.1" 200 - 7 0.007 0.019
2025-07-28 22:17:39,396 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:17:39] "GET /website/translations/4cc7836a09af198dcd075d7696d40a5318e768a6 HTTP/1.1" 200 - 8 0.009 0.012
2025-07-28 22:17:47,768 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:17:47] "GET /price_checker HTTP/1.1" 200 - 8 0.014 0.020
2025-07-28 22:17:47,906 153376 INFO ? werkzeug: 127.0.0.1 - - [28/Jul/2025 22:17:47] "GET /price_checker_kiosk/static/src/css/price_checker.css HTTP/1.1" 200 - 0 0.000 0.014
2025-07-28 22:17:47,928 153376 INFO ? werkzeug: 127.0.0.1 - - [28/Jul/2025 22:17:47] "GET /web/static/src/libs/fontawesome/fonts/fontawesome-webfont.woff2?v=4.7.0 HTTP/1.1" 200 - 0 0.000 0.048
2025-07-28 22:17:47,952 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:17:47] "GET /web/assets/aaef59a/web.assets_frontend_minimal.min.js HTTP/1.1" 200 - 7 0.035 0.033
2025-07-28 22:17:47,953 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:17:47] "GET /web/assets/ea65053/web.assets_frontend.min.css HTTP/1.1" 200 - 7 0.032 0.034
2025-07-28 22:17:47,976 153376 INFO ? werkzeug: 127.0.0.1 - - [28/Jul/2025 22:17:47] "GET /price_checker_kiosk/static/src/img/cubes_logo.png HTTP/1.1" 200 - 0 0.000 0.011
2025-07-28 22:17:48,036 153376 INFO ? werkzeug: 127.0.0.1 - - [28/Jul/2025 22:17:48] "GET /web/static/img/favicon.ico HTTP/1.1" 200 - 0 0.000 0.021
2025-07-28 22:17:48,087 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:17:48] "GET /web/assets/5160663/web.assets_frontend_lazy.min.js HTTP/1.1" 200 - 7 0.007 0.008
2025-07-28 22:17:48,205 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:17:48] "GET /website/translations/4cc7836a09af198dcd075d7696d40a5318e768a6 HTTP/1.1" 200 - 8 0.002 0.007
2025-07-28 22:17:58,998 153376 INFO moassLive25_2 odoo.addons.price_checker_kiosk.controllers.main: Searching for barcode: 6253016805365 
2025-07-28 22:17:59,015 153376 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:17:59] "POST /price_checker/search HTTP/1.1" 200 - 14 0.007 0.019
2025-07-28 22:18:12,821 193988 INFO ? odoo: Odoo version 18.0-20241204 
2025-07-28 22:18:12,822 193988 INFO ? odoo: Using configuration file at C:\odoo18\server\odoo.conf 
2025-07-28 22:18:12,822 193988 INFO ? odoo: addons paths: ['C:\\odoo18\\server\\odoo\\addons', 'c:\\odoo18\\sessions\\addons\\18.0', 'c:\\odoo18\\server\\enterprise183', 'c:\\odoo18\\server\\custom_moass_live_5', 'c:\\odoo18\\server\\odoo\\addons'] 
2025-07-28 22:18:12,822 193988 INFO ? odoo: database: openpg@localhost:5432 
2025-07-28 22:18:13,429 193988 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo18\thirdparty\wkhtmltopdf.exe 
2025-07-28 22:18:13,460 193988 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-07-28 22:18:15,048 193988 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8090 
2025-07-28 22:18:15,170 193988 INFO moassLive25_2 odoo.modules.loading: loading 1 modules... 
2025-07-28 22:18:15,222 193988 INFO moassLive25_2 odoo.modules.loading: 1 modules loaded in 0.05s, 0 queries (+0 extra) 
2025-07-28 22:18:15,276 193988 INFO moassLive25_2 odoo.modules.loading: updating modules list 
2025-07-28 22:18:15,296 193988 INFO moassLive25_2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-28 22:18:15,786 193988 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'app_api', defaulting to LGPL-3 
2025-07-28 22:18:15,935 193988 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'branch_cost', defaulting to LGPL-3 
2025-07-28 22:18:15,999 193988 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'cubes_move_restrictions', defaulting to LGPL-3 
2025-07-28 22:18:16,138 193988 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-07-28 22:18:19,375 193988 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'payment_reports_custom', defaulting to LGPL-3 
2025-07-28 22:18:20,114 193988 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'reconciliation_report', defaulting to LGPL-3 
2025-07-28 22:18:21,813 193988 INFO moassLive25_2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['Price Checker Kiosk'] to user __system__ #1 via n/a 
2025-07-28 22:18:21,813 193988 INFO moassLive25_2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['Price Checker Kiosk'] to user __system__ #1 via n/a 
2025-07-28 22:18:26,767 193988 INFO moassLive25_2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-28 22:18:26,872 193988 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.users', <function Users.context_get at 0x000001FB5D159940>, 1)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x000001FB5D07F560>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-07-28 22:18:26,952 193988 INFO moassLive25_2 odoo.modules.loading: loading 251 modules... 
2025-07-28 22:18:26,970 193988 WARNING moassLive25_2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-28 22:18:27,644 193988 INFO moassLive25_2 odoo.modules.loading: Loading module price_checker_kiosk (55/251) 
2025-07-28 22:18:27,751 193988 INFO moassLive25_2 odoo.modules.loading: loading price_checker_kiosk/security/ir.model.access.csv 
2025-07-28 22:18:27,751 193988 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x000001FB5D07F560>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-07-28 22:18:28,019 193988 INFO moassLive25_2 odoo.modules.loading: loading price_checker_kiosk/views/price_checker_templates.xml 
2025-07-28 22:18:28,035 193988 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x000001FB5D07F560>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-07-28 22:18:28,042 193988 INFO moassLive25_2 odoo.addons.base.models.ir_module: module price_checker_kiosk: no translation for language ar_001 
2025-07-28 22:18:28,052 193988 INFO moassLive25_2 odoo.modules.loading: Module price_checker_kiosk loaded in 0.41s, 50 queries (+50 other) 
2025-07-28 22:18:29,282 193988 WARNING moassLive25_2 py.warnings: C:\odoo18\server\odoo\api.py:466: DeprecationWarning: The model odoo.addons.branch_management.models.purchase is not overriding the create method in batch
  File "C:\odoo18\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\odoo18\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\odoo18\server\odoo\cli\server.py", line 180, in run
    main(args)
  File "C:\odoo18\server\odoo\cli\server.py", line 173, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\odoo18\server\odoo\service\server.py", line 1402, in start
    rc = server.run(preload, stop)
  File "C:\odoo18\server\odoo\service\server.py", line 577, in run
    rc = preload_registries(preload)
  File "C:\odoo18\server\odoo\service\server.py", line 1306, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18\server\odoo\modules\loading.py", line 476, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\odoo18\server\odoo\modules\loading.py", line 364, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\odoo18\server\odoo\modules\loading.py", line 185, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo18\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "c:\odoo18\server\custom_moass_live_5\branch_management\__init__.py", line 1, in <module>
    from . import models
  File "c:\odoo18\server\custom_moass_live_5\branch_management\models\__init__.py", line 6, in <module>
    from . import purchase
  File "c:\odoo18\server\custom_moass_live_5\branch_management\models\purchase.py", line 3, in <module>
    class PurchaseOrder(models.Model):
  File "c:\odoo18\server\custom_moass_live_5\branch_management\models\purchase.py", line 72, in PurchaseOrder
    @api.model
  File "C:\odoo18\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\odoo18\server\odoo\api.py", line 466, in model_create_single
    warnings.warn(
 
2025-07-28 22:18:29,495 193988 WARNING moassLive25_2 odoo.models: The model notification.center has no _description 
2025-07-28 22:18:29,560 193988 WARNING moassLive25_2 odoo.models: The model location.stock.wizard has no _description 
2025-07-28 22:18:29,595 193988 INFO moassLive25_2 odoo.modules.loading: 251 modules loaded in 2.64s, 50 queries (+50 extra) 
2025-07-28 22:18:29,598 193988 INFO moassLive25_2 odoo.modules.loading: loading 251 modules... 
2025-07-28 22:18:29,599 193988 INFO moassLive25_2 odoo.modules.loading: 251 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-28 22:18:29,602 193988 INFO moassLive25_2 odoo.modules.loading: loading 251 modules... 
2025-07-28 22:18:29,602 193988 INFO moassLive25_2 odoo.modules.loading: 251 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-28 22:18:29,731 193988 WARNING moassLive25_2 odoo.fields: Field vip.transfer.partner_id: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:18:29,731 193988 WARNING moassLive25_2 odoo.fields: Field vip.transfer.source_warehouse_id: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:18:29,731 193988 WARNING moassLive25_2 odoo.fields: Field vip.transfer.state: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:18:29,731 193988 WARNING moassLive25_2 odoo.fields: Field notification.center.notification_type: unknown parameter 'states', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:18:29,731 193988 WARNING moassLive25_2 odoo.fields: Field notification.center.user_ids: unknown parameter 'states', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:18:29,731 193988 WARNING moassLive25_2 odoo.fields: Field branch.cost.report.branch_id with unknown comodel_name 'res.branch' 
2025-07-28 22:18:29,731 193988 WARNING moassLive25_2 odoo.fields: Field branch.transfer.state: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:18:29,967 193988 WARNING moassLive25_2 odoo.fields: Field vip.transfer.partner_id: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:18:29,967 193988 WARNING moassLive25_2 odoo.fields: Field vip.transfer.source_warehouse_id: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:18:29,967 193988 WARNING moassLive25_2 odoo.fields: Field vip.transfer.state: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:18:29,967 193988 WARNING moassLive25_2 odoo.fields: Field notification.center.notification_type: unknown parameter 'states', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:18:29,967 193988 WARNING moassLive25_2 odoo.fields: Field notification.center.user_ids: unknown parameter 'states', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:18:29,967 193988 WARNING moassLive25_2 odoo.fields: Field branch.transfer.state: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-28 22:18:30,024 193988 INFO moassLive25_2 odoo.modules.loading: Model account.followup.report.handler is declared but cannot be loaded! (Perhaps a module was partially removed or renamed) 
2025-07-28 22:18:30,111 193988 INFO moassLive25_2 odoo.modules.loading: Model account.customer.statement.report.handler is declared but cannot be loaded! (Perhaps a module was partially removed or renamed) 
2025-07-28 22:18:30,532 193988 WARNING moassLive25_2 py.warnings: C:\odoo18\server\odoo\fields.py:834: UserWarning: Field 'partner.ledger.line.env_ref_lines' in dependency of partner.ledger.line.ending_balance should be searchable. This is necessary to determine which records to recompute when partner.ledger.line.balance is modified. You should either make the field searchable, or simplify the field dependency.
  File "C:\odoo18\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\odoo18\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\odoo18\server\odoo\cli\server.py", line 180, in run
    main(args)
  File "C:\odoo18\server\odoo\cli\server.py", line 173, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\odoo18\server\odoo\service\server.py", line 1402, in start
    rc = server.run(preload, stop)
  File "C:\odoo18\server\odoo\service\server.py", line 577, in run
    rc = preload_registries(preload)
  File "C:\odoo18\server\odoo\service\server.py", line 1306, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18\server\odoo\modules\loading.py", line 583, in load_modules
    env['res.groups']._update_user_groups_view()
  File "C:\odoo18\server\odoo\addons\base\models\res_users.py", line 1794, in _update_user_groups_view
    view.with_context(new_context).write({'arch': xml_content})
  File "c:\odoo18\server\enterprise183\web_studio\models\studio_mixin.py", line 33, in write
    res = super(StudioMixin, self).write(vals)
  File "C:\odoo18\server\odoo\addons\base\models\ir_ui_view.py", line 537, in write
    res = super(View, self).write(self._compute_defaults(vals))
  File "C:\odoo18\server\odoo\models.py", line 4677, in write
    if self.pool.is_modifying_relations(field):
  File "C:\odoo18\server\odoo\modules\registry.py", line 533, in is_modifying_relations
    result = field in self._field_triggers and (
  File "C:\odoo18\server\odoo\tools\func.py", line 42, in __get__
    value = self.fget(obj)
  File "C:\odoo18\server\odoo\modules\registry.py", line 514, in _field_triggers
    dependencies = list(field.resolve_depends(self))
  File "C:\odoo18\server\odoo\fields.py", line 834, in resolve_depends
    warnings.warn(
 
2025-07-28 22:18:32,797 193988 INFO moassLive25_2 odoo.modules.loading: Modules loaded. 
2025-07-28 22:18:32,812 193988 INFO moassLive25_2 odoo.modules.registry: Registry changed, signaling through the database 
2025-07-28 22:18:32,814 193988 INFO moassLive25_2 odoo.modules.registry: Registry loaded in 17.763s 
2025-07-28 22:18:32,815 193988 INFO moassLive25_2 odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-07-28 22:18:32,884 193988 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x000001FB5D07F560>, 'url_code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x000001FB5D07F560>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-07-28 22:18:32,975 193988 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:18:32] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 4 0.015 15.470
2025-07-28 22:18:33,036 193988 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'app_api', defaulting to LGPL-3 
2025-07-28 22:18:33,050 193988 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'cubes_move_restrictions', defaulting to LGPL-3 
2025-07-28 22:18:33,073 193988 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'reconciliation_report', defaulting to LGPL-3 
2025-07-28 22:18:33,081 193988 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'payment_reports_custom', defaulting to LGPL-3 
2025-07-28 22:18:33,090 193988 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'branch_cost', defaulting to LGPL-3 
2025-07-28 22:18:33,127 193988 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-07-28 22:18:33,236 193988 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:18:33] "GET /price_checker HTTP/1.1" 200 - 57 0.072 16.988
2025-07-28 22:18:33,394 193988 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-07-28 22:21:16,343 193988 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:21:16] "GET /price_checker HTTP/1.1" 200 - 8 0.016 0.014
2025-07-28 22:21:32,351 193988 INFO moassLive25_2 odoo.addons.price_checker_kiosk.controllers.main: Searching for barcode: 6253016805365 
2025-07-28 22:21:32,516 193988 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:21:32] "POST /price_checker/search HTTP/1.1" 200 - 13 0.097 0.078
2025-07-28 22:21:50,960 193988 INFO moassLive25_2 odoo.addons.price_checker_kiosk.controllers.main: Searching for barcode: 6253016805365 
2025-07-28 22:21:50,988 193988 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:21:50] "POST /price_checker/search HTTP/1.1" 200 - 12 0.008 0.035
2025-07-28 22:22:52,202 193988 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:22:52] "GET /price_checker HTTP/1.1" 200 - 6 0.010 0.012
2025-07-28 22:22:52,870 193988 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:22:52] "GET /web/assets/ea65053/web.assets_frontend.min.css HTTP/1.1" 200 - 4 0.010 0.430
2025-07-28 22:22:52,894 193988 INFO ? werkzeug: 127.0.0.1 - - [28/Jul/2025 22:22:52] "GET /price_checker_kiosk/static/src/img/cubes_logo.png HTTP/1.1" 200 - 0 0.000 0.012
2025-07-28 22:22:52,939 193988 INFO ? werkzeug: 127.0.0.1 - - [28/Jul/2025 22:22:52] "GET /web/static/src/libs/fontawesome/fonts/fontawesome-webfont.woff2?v=4.7.0 HTTP/1.1" 200 - 0 0.000 0.407
2025-07-28 22:22:52,939 193988 INFO ? werkzeug: 127.0.0.1 - - [28/Jul/2025 22:22:52] "GET /price_checker_kiosk/static/src/css/price_checker.css HTTP/1.1" 200 - 0 0.000 0.409
2025-07-28 22:22:53,229 193988 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:22:53] "GET /web/assets/aaef59a/web.assets_frontend_minimal.min.js HTTP/1.1" 200 - 4 0.010 0.008
2025-07-28 22:22:53,564 193988 INFO ? werkzeug: 127.0.0.1 - - [28/Jul/2025 22:22:53] "GET /web/static/img/favicon.ico HTTP/1.1" 200 - 0 0.000 0.003
2025-07-28 22:22:53,569 193988 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:22:53] "GET /web/assets/5160663/web.assets_frontend_lazy.min.js HTTP/1.1" 200 - 4 0.002 0.005
2025-07-28 22:22:53,842 193988 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:22:53] "GET /website/translations/4cc7836a09af198dcd075d7696d40a5318e768a6 HTTP/1.1" 200 - 5 0.003 0.011
2025-07-28 22:23:47,529 193988 INFO moassLive25_2 odoo.addons.price_checker_kiosk.controllers.main: Searching for barcode: 6253016805365 
2025-07-28 22:23:47,577 193988 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:23:47] "POST /price_checker/search HTTP/1.1" 200 - 11 0.014 0.043
2025-07-28 22:24:01,515 193988 INFO moassLive25_2 odoo.addons.price_checker_kiosk.controllers.main: Searching for barcode: 6253016805365 
2025-07-28 22:24:01,564 193988 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [28/Jul/2025 22:24:01] "POST /price_checker/search HTTP/1.1" 200 - 11 0.014 0.043
2025-07-30 17:01:04,251 222332 INFO ? odoo: Odoo version 18.0-20241204 
2025-07-30 17:01:04,251 222332 INFO ? odoo: Using configuration file at C:\odoo18\server\odoo.conf 
2025-07-30 17:01:04,251 222332 INFO ? odoo: addons paths: ['C:\\odoo18\\server\\odoo\\addons', 'c:\\odoo18\\sessions\\addons\\18.0', 'c:\\odoo18\\server\\enterprise183', 'c:\\odoo18\\server\\custom_moass_live_5', 'c:\\odoo18\\server\\odoo\\addons'] 
2025-07-30 17:01:04,252 222332 INFO ? odoo: database: openpg@localhost:5432 
2025-07-30 17:01:04,751 222332 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo18\thirdparty\wkhtmltopdf.exe 
2025-07-30 17:01:04,768 222332 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-07-30 17:01:06,513 222332 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8090 
2025-07-30 17:01:06,816 222332 INFO moassLive25_2 odoo.modules.loading: loading 1 modules... 
2025-07-30 17:01:06,860 222332 INFO moassLive25_2 odoo.modules.loading: 1 modules loaded in 0.04s, 0 queries (+0 extra) 
2025-07-30 17:01:06,907 222332 INFO moassLive25_2 odoo.modules.loading: updating modules list 
2025-07-30 17:01:06,955 222332 INFO moassLive25_2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-30 17:01:08,203 222332 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'app_api', defaulting to LGPL-3 
2025-07-30 17:01:08,838 222332 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'branch_cost', defaulting to LGPL-3 
2025-07-30 17:01:09,286 222332 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'cubes_move_restrictions', defaulting to LGPL-3 
2025-07-30 17:01:10,320 222332 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-07-30 17:01:24,530 222332 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'payment_reports_custom', defaulting to LGPL-3 
2025-07-30 17:01:28,001 222332 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'reconciliation_report', defaulting to LGPL-3 
2025-07-30 17:01:37,718 222332 INFO moassLive25_2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['Price Checker Kiosk'] to user __system__ #1 via n/a 
2025-07-30 17:01:37,719 222332 INFO moassLive25_2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['Price Checker Kiosk'] to user __system__ #1 via n/a 
2025-07-30 17:01:43,831 222332 INFO moassLive25_2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-30 17:01:44,057 222332 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.users', <function Users.context_get at 0x0000021E70D34180>, 1)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x0000021E70C4DDA0>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-07-30 17:01:44,347 222332 INFO moassLive25_2 odoo.modules.loading: loading 251 modules... 
2025-07-30 17:01:44,393 222332 WARNING moassLive25_2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-30 17:01:47,758 222332 INFO moassLive25_2 odoo.modules.loading: Loading module price_checker_kiosk (55/251) 
2025-07-30 17:01:48,182 222332 INFO moassLive25_2 odoo.modules.loading: loading price_checker_kiosk/security/ir.model.access.csv 
2025-07-30 17:01:48,320 222332 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x0000021E70C4DDA0>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-07-30 17:01:49,671 222332 INFO moassLive25_2 odoo.modules.loading: loading price_checker_kiosk/views/price_checker_templates.xml 
2025-07-30 17:01:49,899 222332 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x0000021E70C4DDA0>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-07-30 17:01:49,908 222332 INFO moassLive25_2 odoo.addons.base.models.ir_module: module price_checker_kiosk: no translation for language ar_001 
2025-07-30 17:01:49,926 222332 INFO moassLive25_2 odoo.modules.loading: Module price_checker_kiosk loaded in 2.17s, 49 queries (+49 other) 
2025-07-30 17:01:53,426 222332 WARNING moassLive25_2 py.warnings: C:\odoo18\server\odoo\api.py:466: DeprecationWarning: The model odoo.addons.branch_management.models.purchase is not overriding the create method in batch
  File "C:\odoo18\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\odoo18\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\odoo18\server\odoo\cli\server.py", line 180, in run
    main(args)
  File "C:\odoo18\server\odoo\cli\server.py", line 173, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\odoo18\server\odoo\service\server.py", line 1402, in start
    rc = server.run(preload, stop)
  File "C:\odoo18\server\odoo\service\server.py", line 577, in run
    rc = preload_registries(preload)
  File "C:\odoo18\server\odoo\service\server.py", line 1306, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18\server\odoo\modules\loading.py", line 476, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\odoo18\server\odoo\modules\loading.py", line 364, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\odoo18\server\odoo\modules\loading.py", line 185, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo18\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "c:\odoo18\server\custom_moass_live_5\branch_management\__init__.py", line 1, in <module>
    from . import models
  File "c:\odoo18\server\custom_moass_live_5\branch_management\models\__init__.py", line 6, in <module>
    from . import purchase
  File "c:\odoo18\server\custom_moass_live_5\branch_management\models\purchase.py", line 3, in <module>
    class PurchaseOrder(models.Model):
  File "c:\odoo18\server\custom_moass_live_5\branch_management\models\purchase.py", line 72, in PurchaseOrder
    @api.model
  File "C:\odoo18\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\odoo18\server\odoo\api.py", line 466, in model_create_single
    warnings.warn(
 
2025-07-30 17:01:53,852 222332 WARNING moassLive25_2 odoo.models: The model notification.center has no _description 
2025-07-30 17:01:53,999 222332 WARNING moassLive25_2 odoo.models: The model location.stock.wizard has no _description 
2025-07-30 17:01:54,077 222332 INFO moassLive25_2 odoo.modules.loading: 251 modules loaded in 9.73s, 49 queries (+49 extra) 
2025-07-30 17:01:54,082 222332 INFO moassLive25_2 odoo.modules.loading: loading 251 modules... 
2025-07-30 17:01:54,082 222332 INFO moassLive25_2 odoo.modules.loading: 251 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-30 17:01:54,082 222332 INFO moassLive25_2 odoo.modules.loading: loading 251 modules... 
2025-07-30 17:01:54,082 222332 INFO moassLive25_2 odoo.modules.loading: 251 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-30 17:01:54,341 222332 WARNING moassLive25_2 odoo.fields: Field vip.transfer.partner_id: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-30 17:01:54,341 222332 WARNING moassLive25_2 odoo.fields: Field vip.transfer.source_warehouse_id: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-30 17:01:54,342 222332 WARNING moassLive25_2 odoo.fields: Field vip.transfer.state: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-30 17:01:54,342 222332 WARNING moassLive25_2 odoo.fields: Field notification.center.notification_type: unknown parameter 'states', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-30 17:01:54,342 222332 WARNING moassLive25_2 odoo.fields: Field notification.center.user_ids: unknown parameter 'states', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-30 17:01:54,342 222332 WARNING moassLive25_2 odoo.fields: Field branch.cost.report.branch_id with unknown comodel_name 'res.branch' 
2025-07-30 17:01:54,342 222332 WARNING moassLive25_2 odoo.fields: Field branch.transfer.state: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-30 17:01:54,796 222332 WARNING moassLive25_2 odoo.fields: Field vip.transfer.partner_id: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-30 17:01:54,796 222332 WARNING moassLive25_2 odoo.fields: Field vip.transfer.source_warehouse_id: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-30 17:01:54,796 222332 WARNING moassLive25_2 odoo.fields: Field vip.transfer.state: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-30 17:01:54,797 222332 WARNING moassLive25_2 odoo.fields: Field notification.center.notification_type: unknown parameter 'states', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-30 17:01:54,797 222332 WARNING moassLive25_2 odoo.fields: Field notification.center.user_ids: unknown parameter 'states', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-30 17:01:54,798 222332 WARNING moassLive25_2 odoo.fields: Field branch.transfer.state: unknown parameter 'tracking', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-07-30 17:01:54,883 222332 INFO moassLive25_2 odoo.modules.loading: Model account.followup.report.handler is declared but cannot be loaded! (Perhaps a module was partially removed or renamed) 
2025-07-30 17:01:54,980 222332 INFO moassLive25_2 odoo.modules.loading: Model account.customer.statement.report.handler is declared but cannot be loaded! (Perhaps a module was partially removed or renamed) 
2025-07-30 17:01:55,612 222332 WARNING moassLive25_2 py.warnings: C:\odoo18\server\odoo\fields.py:834: UserWarning: Field 'partner.ledger.line.env_ref_lines' in dependency of partner.ledger.line.ending_balance should be searchable. This is necessary to determine which records to recompute when partner.ledger.line.balance is modified. You should either make the field searchable, or simplify the field dependency.
  File "C:\odoo18\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\odoo18\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\odoo18\server\odoo\cli\server.py", line 180, in run
    main(args)
  File "C:\odoo18\server\odoo\cli\server.py", line 173, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\odoo18\server\odoo\service\server.py", line 1402, in start
    rc = server.run(preload, stop)
  File "C:\odoo18\server\odoo\service\server.py", line 577, in run
    rc = preload_registries(preload)
  File "C:\odoo18\server\odoo\service\server.py", line 1306, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\odoo18\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo18\server\odoo\modules\loading.py", line 583, in load_modules
    env['res.groups']._update_user_groups_view()
  File "C:\odoo18\server\odoo\addons\base\models\res_users.py", line 1794, in _update_user_groups_view
    view.with_context(new_context).write({'arch': xml_content})
  File "c:\odoo18\server\enterprise183\web_studio\models\studio_mixin.py", line 33, in write
    res = super(StudioMixin, self).write(vals)
  File "C:\odoo18\server\odoo\addons\base\models\ir_ui_view.py", line 537, in write
    res = super(View, self).write(self._compute_defaults(vals))
  File "C:\odoo18\server\odoo\models.py", line 4677, in write
    if self.pool.is_modifying_relations(field):
  File "C:\odoo18\server\odoo\modules\registry.py", line 533, in is_modifying_relations
    result = field in self._field_triggers and (
  File "C:\odoo18\server\odoo\tools\func.py", line 42, in __get__
    value = self.fget(obj)
  File "C:\odoo18\server\odoo\modules\registry.py", line 514, in _field_triggers
    dependencies = list(field.resolve_depends(self))
  File "C:\odoo18\server\odoo\fields.py", line 834, in resolve_depends
    warnings.warn(
 
2025-07-30 17:01:58,051 222332 INFO moassLive25_2 odoo.modules.loading: Modules loaded. 
2025-07-30 17:01:58,076 222332 INFO moassLive25_2 odoo.modules.registry: Registry changed, signaling through the database 
2025-07-30 17:01:58,077 222332 INFO moassLive25_2 odoo.modules.registry: Registry loaded in 51.561s 
2025-07-30 17:01:58,081 222332 INFO moassLive25_2 odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-07-30 17:01:58,164 222332 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x0000021E70C4DDA0>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-07-30 17:01:58,167 222332 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:01:58] "GET / HTTP/1.1" 303 - 6 0.006 29.848
2025-07-30 17:01:58,540 222332 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:01:58] "GET /odoo HTTP/1.1" 303 - 1 0.002 0.006
2025-07-30 17:01:58,921 222332 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-07-30 17:01:58,933 222332 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'cubes_move_restrictions', defaulting to LGPL-3 
2025-07-30 17:01:59,024 222332 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'reconciliation_report', defaulting to LGPL-3 
2025-07-30 17:01:59,032 222332 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'app_api', defaulting to LGPL-3 
2025-07-30 17:01:59,050 222332 WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'branch_cost', defaulting to LGPL-3 
2025-07-30 17:01:59,********** WARNING moassLive25_2 odoo.modules.module: Missing `license` key in manifest for 'payment_reports_custom', defaulting to LGPL-3 
2025-07-30 17:01:59,********** INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:01:59] "GET /web/login?redirect=/odoo? HTTP/1.1" 200 - 58 0.086 0.577
2025-07-30 17:01:59,********** INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:01:59] "GET /web/binary/company_logo HTTP/1.1" 200 - 2 0.006 0.274
2025-07-30 17:02:00,********** INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:00] "GET /website/translations/4cc7836a09af198dcd075d7696d40a5318e768a6 HTTP/1.1" 200 - 6 0.016 0.016
2025-07-30 17:02:02,********** INFO moassLive25_2 odoo.addons.base.models.res_users: Login successful for db:moassLive25_2 login:admin from 127.0.0.1 
2025-07-30 17:02:02,********** INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.users', <function Users.context_get at 0x0000021E70D34180>, 2)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang._get_active_by at 0x0000021E70C4DDA0>, 'code')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-07-30 17:02:02,321 222332 INFO moassLive25_2 odoo.modules.registry: Caches invalidated, signaling through the database: ['default'] 
2025-07-30 17:02:02,321 222332 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:02] "POST /web/login HTTP/1.1" 303 - 47 0.124 0.764
2025-07-30 17:02:02,353 222332 INFO moassLive25_2 odoo.addons.base.models.res_device: User 2 inserts device log (MjVOagisR4W7GnM9M0KB0Gp3-dfa3HljmYC6Z3Tpth) 
2025-07-30 17:02:02,871 222332 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\25/25eab71cf1a7250397d5f1b39bbdb69315983ba2 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000021E703F02C0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\25/25eab71cf1a7250397d5f1b39bbdb69315983ba2'
2025-07-30 17:02:02,872 222332 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\d9/d91cb0023dd0de9111309aa17eddcd5caff2fa91 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000021E703F02C0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\d9/d91cb0023dd0de9111309aa17eddcd5caff2fa91'
2025-07-30 17:02:02,873 222332 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\ca/cad9804f210a8c1e26c8ebab07b8056b9d5ec66b 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000021E703F02C0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\ca/cad9804f210a8c1e26c8ebab07b8056b9d5ec66b'
2025-07-30 17:02:02,874 222332 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\70/70633333ff0c9230f06ebf2b3bf7770df6d800d0 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000021E703F02C0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\70/70633333ff0c9230f06ebf2b3bf7770df6d800d0'
2025-07-30 17:02:02,874 222332 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\37/3709cc2e0d5df4c199f96c6d07e2aaf29a63bcba 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000021E703F02C0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\37/3709cc2e0d5df4c199f96c6d07e2aaf29a63bcba'
2025-07-30 17:02:02,875 222332 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\c2/c21721ec3ec2e8d086eebf804e0ede5f25aa14d7 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000021E703F02C0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\c2/c21721ec3ec2e8d086eebf804e0ede5f25aa14d7'
2025-07-30 17:02:02,875 222332 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\f9/f9cf2c8b67d3ebc89ca4d180ef207a248c4e3130 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000021E703F02C0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\f9/f9cf2c8b67d3ebc89ca4d180ef207a248c4e3130'
2025-07-30 17:02:02,876 222332 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\7d/7d36617149ed2fe93e9ba984cffe16f468bd7a0e 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000021E703F02C0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\7d/7d36617149ed2fe93e9ba984cffe16f468bd7a0e'
2025-07-30 17:02:02,877 222332 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\c2/c2f49a19f3cf050243e62b22cde5072cca84fcaa 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000021E703F02C0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\c2/c2f49a19f3cf050243e62b22cde5072cca84fcaa'
2025-07-30 17:02:02,878 222332 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\67/670e9ff9033932ebe066245e9ac5c3382cde45f4 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000021E703F02C0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\67/670e9ff9033932ebe066245e9ac5c3382cde45f4'
2025-07-30 17:02:02,878 222332 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\51/517a97a04c19cdd67572ff8a0ef89fad5a2c278e 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000021E703F02C0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\51/517a97a04c19cdd67572ff8a0ef89fad5a2c278e'
2025-07-30 17:02:02,879 222332 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\1c/1c96f0109a19f3ff4c0a7486431a79da8c42d8de 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000021E703F02C0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\1c/1c96f0109a19f3ff4c0a7486431a79da8c42d8de'
2025-07-30 17:02:02,881 222332 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\50/503be145dcd1c8f6491ae855820d93b8ab8363da 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000021E703F02C0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\50/503be145dcd1c8f6491ae855820d93b8ab8363da'
2025-07-30 17:02:02,882 222332 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\1c/1c16002d10f6155661a6ae83417fee29e281fb7b 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000021E703F02C0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\1c/1c16002d10f6155661a6ae83417fee29e281fb7b'
2025-07-30 17:02:02,883 222332 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\a4/a4511b76524b49a86d44d27508dd20f056bfe827 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000021E703F02C0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\a4/a4511b76524b49a86d44d27508dd20f056bfe827'
2025-07-30 17:02:02,********** INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\f3/f31bdf173e5748b875ca5e389e1edba54b4bff25 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000021E703F02C0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\f3/f31bdf173e5748b875ca5e389e1edba54b4bff25'
2025-07-30 17:02:02,********** INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\74/744fc247b813c44f4cf02357706e88b283cb0f97 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000021E703F02C0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\74/744fc247b813c44f4cf02357706e88b283cb0f97'
2025-07-30 17:02:02,********** INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\3d/3d7a7360fe4a73a90dd3b4ba554a9254ed61e864 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000021E703F02C0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\3d/3d7a7360fe4a73a90dd3b4ba554a9254ed61e864'
2025-07-30 17:02:02,886 222332 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\b7/b73a9deb85def8a1a00e4d545c4f79747d78f487 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\tools\cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo18\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo18\server\odoo\tools\lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000021E703F02C0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\b7/b73a9deb85def8a1a00e4d545c4f79747d78f487'
2025-07-30 17:02:04,116 222332 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:04] "GET /odoo HTTP/1.1" 200 - 128 0.163 1.620
2025-07-30 17:02:04,683 222332 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:04] "GET /web/webclient/load_menus/c4b89756a504b7a0757da28c4e9402b9c47fdedd3d3d8e6e01f9af48a66cbf64 HTTP/1.1" 200 - 260 0.114 0.123
2025-07-30 17:02:04,909 222332 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:04] "POST /mail/data HTTP/1.1" 200 - 48 0.163 0.055
2025-07-30 17:02:04,958 222332 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:04] "POST /hr_attendance/attendance_user_data HTTP/1.1" 200 - 7 0.028 0.051
2025-07-30 17:02:04,972 222332 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:04] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.010 0.055
2025-07-30 17:02:04,985 222332 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\10/10b00a9b7504892eb1bca5d9b6c2848842d67ea6 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\10/10b00a9b7504892eb1bca5d9b6c2848842d67ea6'
2025-07-30 17:02:04,985 222332 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:04] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 16 0.017 0.056
2025-07-30 17:02:05,062 222332 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:05] "GET /bus/websocket_worker_bundle?v=18.0-2 HTTP/1.1" 304 - 4 0.000 0.040
2025-07-30 17:02:05,402 222332 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:05] "GET /websocket?version=18.0-2 HTTP/1.1" 101 - 1 0.000 0.005
2025-07-30 17:02:05,434 222332 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-07-30 17:02:26,434 222332 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:26] "POST /web/action/load HTTP/1.1" 200 - 12 0.050 0.031
2025-07-30 17:02:27,242 222332 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:27] "POST /web/dataset/call_kw/sale.order/get_views HTTP/1.1" 200 - 153 0.153 0.294
2025-07-30 17:02:27,361 222332 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:27] "POST /web/dataset/call_kw/sale.order/web_search_read HTTP/1.1" 200 - 22 0.047 0.054
2025-07-30 17:02:27,570 222332 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:27] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 1 0.000 0.000
2025-07-30 17:02:28,065 222332 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:28] "POST /web/action/load HTTP/1.1" 200 - 10 0.029 0.060
2025-07-30 17:02:28,146 222332 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\10/10b00a9b7504892eb1bca5d9b6c2848842d67ea6 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\10/10b00a9b7504892eb1bca5d9b6c2848842d67ea6'
2025-07-30 17:02:28,157 222332 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:28] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 9 0.026 0.096
2025-07-30 17:02:28,********** INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:28] "POST /web/dataset/call_kw/customer.balance.by.branch.wizard/get_views HTTP/1.1" 200 - 10 0.018 0.003
2025-07-30 17:02:28,********** INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:28] "POST /web/dataset/call_kw/customer.balance.by.branch.wizard/onchange HTTP/1.1" 200 - 2 0.000 0.010
2025-07-30 17:02:30,********** INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:30] "POST /web/dataset/call_kw/company.branch/name_search HTTP/1.1" 200 - 8 0.004 0.024
2025-07-30 17:02:44,********** INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:44] "POST /web/dataset/call_kw/customer.balance.by.branch.wizard/web_save HTTP/1.1" 200 - 6 0.031 0.024
2025-07-30 17:02:46,********** INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:46] "POST /web/dataset/call_button/customer.balance.by.branch.wizard/action_print_report HTTP/1.1" 200 - 585 1.729 0.919
2025-07-30 17:02:46,********** INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:46] "POST /report/check_wkhtmltopdf HTTP/1.1" 200 - 1 0.001 0.005
2025-07-30 17:02:47,********** INFO moassLive25_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo18\sessions\filestore\moassLive25_2\e3/e35bc779bd2c9e735fc9a564d7ce4e1848c8c340 
Traceback (most recent call last):
  File "C:\odoo18\server\odoo\addons\base\models\ir_attachment.py", line 122, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo18\\sessions\\filestore\\moassLive25_2\\e3/e35bc779bd2c9e735fc9a564d7ce4e1848c8c340'
2025-07-30 17:02:49,645 222332 INFO moassLive25_2 odoo.addons.base.models.ir_actions_report: Generating PDF on Windows platform require DPI >= 96. Using 96 instead. 
2025-07-30 17:02:54,039 222332 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:54] "GET /web/assets/fc9be58/web.report_assets_common.min.css HTTP/1.1" 200 - 3 0.013 0.105
2025-07-30 17:02:54,074 222332 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:54] "GET /web/assets/62b93bd/web.report_assets_pdf.min.css HTTP/1.1" 200 - 3 0.020 0.134
2025-07-30 17:02:54,261 222332 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:54] "GET / HTTP/1.1" 303 - 1 0.003 0.027
2025-07-30 17:02:56,909 222332 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:02:56] "GET /odoo HTTP/1.1" 200 - 23 0.184 0.136
2025-07-30 17:02:58,345 222332 INFO moassLive25_2 odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) starting 
2025-07-30 17:02:58,519 222332 INFO moassLive25_2 odoo.addons.base.models.ir_attachment: filestore gc 42 checked, 37 removed 
2025-07-30 17:02:58,552 222332 INFO moassLive25_2 odoo.models.unlink: User #1 deleted ir.cron.progress records with IDs: [82661, 82662, 82663, 82664, 82665, 82666, 82667, 82668, 82669, 82670, 82671, 82672, 82673, 82674, 82727, 82728, 82750, 82751] 
2025-07-30 17:02:59,158 222332 INFO moassLive25_2 odoo.addons.base.models.res_users: GC'd 2 user log entries 
2025-07-30 17:02:59,173 222332 INFO moassLive25_2 odoo.addons.base.models.res_users: GC 'res.users.apikeys' delete 0 entries 
2025-07-30 17:02:59,204 222332 INFO moassLive25_2 odoo.addons.base.models.res_device: GC device logs delete 3 entries 
2025-07-30 17:02:59,212 222332 INFO moassLive25_2 odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-07-30 17:02:59,224 222332 INFO moassLive25_2 odoo.models.unlink: User #1 deleted base.module.update records with IDs: [171, 172, 173, 174, 175, 176, 177, 178, 179] 
2025-07-30 17:02:59,271 222332 INFO moassLive25_2 odoo.addons.base.models.res_users: GC 'auth_totp.device' delete 0 entries 
2025-07-30 17:02:59,299 222332 INFO moassLive25_2 odoo.models.unlink: User #1 deleted bus.bus records with IDs: [57557, 57558, 57559, 57560, 57561, 57562, 57563, 57564, 57565, 57566, 57567, 57568, 57569, 57570, 57571, 57572, 57573, 57574, 57575, 57576, 57577, 57578, 57579, 57580, 57581, 57582, 57583, 57584, 57585, 57586, 57587, 57588, 57589, 57590, 57591, 57592, 57593, 57594, 57595, 57596, 57597, 57598, 57599, 57600, 57601, 57602, 57603, 57604, 57605, 57606, 57607, 57608, 57609, 57610, 57611, 57612, 57613, 57614, 57615, 57616, 57617, 57618, 57619, 57620, 57621, 57622, 57623, 57624, 57625, 57626] 
2025-07-30 17:02:59,305 222332 INFO moassLive25_2 odoo.addons.base.models.ir_cron: Job 'Attendance: Automatically check-out employees' (56) starting 
2025-07-30 17:02:59,366 222332 INFO moassLive25_2 odoo.addons.base.models.ir_cron: Job 'Attendance: Automatically check-out employees' (56) done in 0.062s 
2025-07-30 17:02:59,378 222332 INFO moassLive25_2 odoo.addons.base.models.ir_cron: Job 'Attendance: Automatically check-out employees' (56) processed 0 records, 0 records remaining 
2025-07-30 17:02:59,425 222332 INFO moassLive25_2 odoo.addons.base.models.ir_cron: Job 'Attendance: Automatically check-out employees' (56) completed 
2025-07-30 17:02:59,441 222332 INFO moassLive25_2 odoo.addons.base.models.ir_cron: Job 'Attendance: Detect Absences for employees' (57) starting 
2025-07-30 17:02:59,472 222332 INFO moassLive25_2 odoo.addons.base.models.ir_cron: Job 'Attendance: Detect Absences for employees' (57) done in 0.014s 
2025-07-30 17:02:59,482 222332 INFO moassLive25_2 odoo.addons.base.models.ir_cron: Job 'Attendance: Detect Absences for employees' (57) processed 0 records, 0 records remaining 
2025-07-30 17:02:59,522 222332 INFO moassLive25_2 odoo.addons.base.models.ir_cron: Job 'Attendance: Detect Absences for employees' (57) completed 
2025-07-30 17:02:59,572 222332 INFO moassLive25_2 odoo.addons.base.models.ir_cron: Job 'Daily Sales Webhook Reports' (80) starting 
2025-07-30 17:02:59,602 222332 INFO moassLive25_2 odoo.addons.base.models.ir_cron: Job 'Daily Sales Webhook Reports' (80) done in 0.030s 
2025-07-30 17:02:59,616 222332 INFO moassLive25_2 odoo.addons.base.models.ir_cron: Job 'Daily Sales Webhook Reports' (80) processed 0 records, 0 records remaining 
2025-07-30 17:02:59,648 222332 INFO moassLive25_2 odoo.addons.base.models.ir_cron: Job 'Daily Sales Webhook Reports' (80) completed 
2025-07-30 17:02:59,681 222332 INFO moassLive25_2 odoo.addons.base.models.ir_cron: Job 'Retry Failed Webhooks' (81) starting 
2025-07-30 17:02:59,699 222332 INFO moassLive25_2 odoo.addons.base.models.ir_cron: Job 'Retry Failed Webhooks' (81) done in 0.019s 
2025-07-30 17:02:59,699 222332 INFO moassLive25_2 odoo.addons.base.models.ir_cron: Job 'Retry Failed Webhooks' (81) processed 0 records, 0 records remaining 
2025-07-30 17:02:59,699 222332 INFO moassLive25_2 odoo.addons.base.models.ir_cron: Job 'Retry Failed Webhooks' (81) completed 
2025-07-30 17:02:59,712 222332 INFO moassLive25_2 odoo.addons.sms.models.sms_sms: GC'd 0 sms marked for deletion 
2025-07-30 17:03:00,385 222332 INFO ? werkzeug: 127.0.0.1 - - [30/Jul/2025 17:03:00] "GET /web/static/fonts/lato/Lato-Lig-webfont.woff HTTP/1.1" 200 - 0 0.000 0.006
2025-07-30 17:03:00,385 222332 INFO ? werkzeug: 127.0.0.1 - - [30/Jul/2025 17:03:00] "GET /web/static/fonts/lato/Lato-Bol-webfont.woff HTTP/1.1" 200 - 0 0.000 0.006
2025-07-30 17:03:00,391 222332 INFO ? werkzeug: 127.0.0.1 - - [30/Jul/2025 17:03:00] "GET /web/static/fonts/lato/Lato-Bla-webfont.woff HTTP/1.1" 200 - 0 0.000 0.012
2025-07-30 17:03:00,391 222332 INFO ? werkzeug: 127.0.0.1 - - [30/Jul/2025 17:03:00] "GET /web/static/fonts/lato/Lato-Hai-webfont.woff HTTP/1.1" 200 - 0 0.000 0.012
2025-07-30 17:03:00,393 222332 INFO ? werkzeug: 127.0.0.1 - - [30/Jul/2025 17:03:00] "GET /web/static/fonts/lato/Lato-Reg-webfont.woff HTTP/1.1" 200 - 0 0.000 0.015
2025-07-30 17:03:00,712 222332 INFO moassLive25_2 odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) done in 2.368s 
2025-07-30 17:03:00,728 222332 INFO moassLive25_2 odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) processed 0 records, 0 records remaining 
2025-07-30 17:03:00,728 222332 INFO moassLive25_2 odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) completed 
2025-07-30 17:03:02,286 222332 WARNING moassLive25_2 odoo.addons.base.models.ir_actions_report: wkhtmltopdf: QWin32PrintEngine::initialize: OpenPrinter failed (The RPC server is unavailable.)
QWin32PrintEngine::initialize: OpenPrinter failed (The RPC server is unavailable.)
 
2025-07-30 17:03:02,286 222332 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:03:02] "POST /report/download HTTP/1.1" 200 - 90 0.322 14.733
2025-07-30 17:03:02,347 222332 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:03:02] "POST /web/dataset/call_kw/customer.balance.by.branch.wizard/web_read HTTP/1.1" 200 - 3 0.000 0.019
2025-07-30 17:03:33,798 222332 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:03:33] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.118
2025-07-30 17:08:12,291 222332 INFO moassLive25_2 werkzeug: 127.0.0.1 - - [30/Jul/2025 17:08:12] "POST /web/dataset/call_button/customer.balance.by.branch.wizard/action_export_excel HTTP/1.1" 200 - 583 1.882 1.717
